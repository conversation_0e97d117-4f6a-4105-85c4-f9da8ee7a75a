<?php

namespace App\Console\Commands;

use App\Models\Course;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateCoursePrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'courses:update-prices {--force : Cập nhật tất cả khóa học bất kể ngày}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật giá khóa học dựa trên price trends đã cấu hình';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Bắt đầu cập nhật giá khóa học...');

        $today = Carbon::now()->format('Y-m-d');
        $force = $this->option('force');

        $query = Course::whereNotNull('price_trends')
                      ->where('is_paid', 1);

        // Nếu không có flag --force, chỉ lấy khóa học có price trend phù hợp với ngày hiện tại
        if (!$force) {
            $query->where(function($q) use ($today) {
                $q->whereRaw("JSON_SEARCH(price_trends, 'one', '{$today}', NULL, '$.start_date') IS NOT NULL");
            });
        }

        $courses = $query->get();

        if ($courses->isEmpty()) {
            $this->info('Không có khóa học nào cần cập nhật giá.');
            return 0;
        }

        $this->info('Tìm thấy ' . $courses->count() . ' khóa học cần kiểm tra.');

        $bar = $this->output->createProgressBar($courses->count());
        $bar->start();

        $updatedCount = 0;

        foreach ($courses as $course) {
            $oldPrice = $course->price;

            // Tìm price trend phù hợp
            $applicableTrend = null;
            $latestDate = null;
            $priceTrends = $course->price_trends;

            // Xử lý price_trends có thể là string hoặc array
            if (is_string($priceTrends)) {
                $priceTrends = json_decode($priceTrends, true);
            } elseif (!is_array($priceTrends)) {
                $priceTrends = [];
            }
            if (!empty($priceTrends)) {
                foreach ($priceTrends as $trend) {
                    if (isset($trend['start_date']) && isset($trend['price'])) {

                        $startDate = $trend['start_date'];

                        // Nếu ngày bắt đầu đã qua và là ngày gần nhất so với hiện tại
                        if ($startDate <= $today && ($latestDate === null || $startDate > $latestDate)) {
                            $applicableTrend = $trend;
                            $latestDate = $startDate;
                        }
                    }
                }

                // Cập nhật giá nếu tìm thấy price trend phù hợp
                if ($applicableTrend && $course->discounted_price != $applicableTrend['price']) {
                    $course->discounted_price = $applicableTrend['price'];
                    $course->save();
                    $updatedCount++;

                    // Log thông tin
                    Log::info("Đã cập nhật giá khóa học ID: {$course->id}, Tên: {$course->title}, Giá cũ: {$oldPrice}, Giá mới: {$course->price}");
                }
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        $this->info("Hoàn thành! Đã cập nhật giá cho {$updatedCount} khóa học.");

        return 0;
    }
}
