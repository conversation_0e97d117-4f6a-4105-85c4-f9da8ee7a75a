<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;

class PricingPlanController extends Controller
{
    /**
     * L<PERSON>y danh sách pricing plans của một khóa học
     */
    public function getCoursePricingPlans($courseId)
    {
        try {
            $course = Course::findOrFail($courseId);
            
            if (!$course->hasMultiplePricingPlans()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Khóa học này không sử dụng multiple pricing plans',
                    'data' => []
                ]);
            }

            $plans = $course->getPricingPlans();
            
            // Thêm thông tin giá hiện tại cho mỗi plan
            foreach ($plans as &$plan) {
                $plan['current_price'] = $course->getPlanCurrentPrice($plan['id']);
                $plan['has_discount'] = isset($plan['discounted_price']) && 
                                       $plan['discounted_price'] > 0 && 
                                       $course->discount_flag == 1;
                
                // Format duration text
                if ($plan['duration_type'] === 'lifetime') {
                    $plan['duration_text'] = 'Trọn đời';
                } else {
                    $plan['duration_text'] = $plan['duration_value'] . ' tháng';
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Lấy danh sách pricing plans thành công',
                'data' => [
                    'course_id' => $course->id,
                    'course_title' => $course->title,
                    'pricing_type' => $course->pricing_type,
                    'plans' => $plans
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Lấy thông tin chi tiết của một pricing plan
     */
    public function getPricingPlanDetails($courseId, $planId)
    {
        try {
            $course = Course::findOrFail($courseId);
            $plan = $course->getPricingPlanById($planId);
            
            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy pricing plan',
                    'data' => null
                ], 404);
            }

            // Thêm thông tin bổ sung
            $plan['current_price'] = $course->getPlanCurrentPrice($planId);
            $plan['has_discount'] = isset($plan['discounted_price']) && 
                                   $plan['discounted_price'] > 0 && 
                                   $course->discount_flag == 1;
            
            if ($plan['duration_type'] === 'lifetime') {
                $plan['duration_text'] = 'Trọn đời';
                $plan['expiry_date'] = null;
            } else {
                $plan['duration_text'] = $plan['duration_value'] . ' tháng';
                $plan['expiry_date'] = $course->calculateExpiryDate($planId);
            }

            return response()->json([
                'success' => true,
                'message' => 'Lấy thông tin pricing plan thành công',
                'data' => $plan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * Tính toán giá cho một pricing plan (có thể áp dụng coupon)
     */
    public function calculatePlanPrice(Request $request, $courseId, $planId)
    {
        try {
            $course = Course::findOrFail($courseId);
            $plan = $course->getPricingPlanById($planId);
            
            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy pricing plan',
                    'data' => null
                ], 404);
            }

            $basePrice = $course->getPlanCurrentPrice($planId);
            $finalPrice = $basePrice;
            $discount = 0;
            $couponCode = null;

            // Áp dụng coupon nếu có
            if ($request->has('coupon_code')) {
                $couponCode = $request->coupon_code;
                
                $coupon = \App\Models\Coupon::where('code', $couponCode)
                    ->where(function ($query) use ($course) {
                        $query->where('course_id', $course->id)
                            ->orWhereNull('course_id');
                    })
                    ->where('expiry', '>=', time())
                    ->whereIn('status', [1, 2])
                    ->where('discount', '>', 0)
                    ->first();

                if ($coupon) {
                    $discount = ($basePrice * $coupon->discount) / 100;
                    $finalPrice = $basePrice - $discount;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Tính toán giá thành công',
                'data' => [
                    'plan_id' => $planId,
                    'plan_name' => $plan['name'],
                    'base_price' => $basePrice,
                    'discount' => $discount,
                    'final_price' => $finalPrice,
                    'coupon_code' => $couponCode,
                    'currency' => currency()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
