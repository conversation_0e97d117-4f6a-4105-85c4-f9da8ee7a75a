<?php

namespace App\Http\Controllers\student;

use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\Coupon;
use App\Models\Course;
use App\Models\OfflinePayment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class CouponController extends Controller
{
    public function applyCoupon(Request $request)
    {
        $course = Course::find($request->input("course_id"));
        if (!$course) {
            return response()->json(['error' => true, 'message' => get_phrase('Course not found')]);
        }

        $coupon = Coupon::where('code', $request->input("coupon_code"))
            ->where(function ($query) use ($course) {
                $query->where('course_id', $course->id)
                    ->orWhereNull('course_id');
            })
            ->where("expiry", ">=", time())
            ->whereIn('status', [1, 2])
            ->where("discount", ">", 0)
            ->first();

        if (!$coupon) {
            return response()->json(['error' => true, 'message' => get_phrase('Invalid coupon code')]);
        }

        if ($coupon->user_aff_id) {
            // Check xem id user ở cookie có trung với aff_user_id hay không
            if (!Cookie::has("affiliate_ref")) {
                return response()->json(['error' => true, 'message' => "Mã coupon không hợp lệ"]);
            }

            $affiliate_ref = Cookie::get("affiliate_ref");
            $affiliate_ref = hex2bin($affiliate_ref);
            $affiliate_ref = str_replace("KH-", "", $affiliate_ref);
            if ($affiliate_ref != $coupon->user_aff_id) {
                return response()->json(['error' => true, 'message' => "Mã coupon không hợp lệ"]);
            }
        }

        // Xử lý pricing plan nếu có
        $responseData = [
            'coupon' => $coupon,
            'course' => $course,
        ];

        // Nếu có pricing_plan_id, tính giá theo plan
        if ($request->has('pricing_plan_id') && $course->hasMultiplePricingPlans()) {
            $planId = $request->input('pricing_plan_id');
            $plan = $course->getPricingPlanById($planId);

            if ($plan) {
                $planCurrentPrice = $course->getPlanCurrentPrice($planId);
                $responseData['pricing_plan'] = [
                    'id' => $planId,
                    'name' => $plan['name'],
                    'original_price' => $plan['price'],
                    'current_price' => $planCurrentPrice,
                    'discount_amount' => ($planCurrentPrice * $coupon->discount) / 100,
                    'final_price' => $planCurrentPrice - (($planCurrentPrice * $coupon->discount) / 100)
                ];
            }
        }

        return response()->json([
            'error' => false,
            "data" => $responseData,
            'message' => get_phrase('Coupon applied successfully')
        ]);
    }

}
