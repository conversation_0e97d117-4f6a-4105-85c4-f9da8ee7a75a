<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $casts = [
        'price_trends' => 'array',
        'pricing_plans' => 'array',
    ];

    /**
     * Safely get pricing plans data
     */
    public function getPricingPlansData()
    {
        $plans = $this->pricing_plans;

        if (is_string($plans)) {
            $plans = json_decode($plans, true);
        }

        return is_array($plans) ? $plans : [];
    }

    /**
     * Safely get price trends data
     */
    public function getPriceTrendsData()
    {
        $trends = $this->price_trends;

        if (is_string($trends)) {
            $trends = json_decode($trends, true);
        }

        return is_array($trends) ? $trends : [];
    }


    public function category()
    {
        return $this->belongsTo(Category::class)->withDefault();
    }

    public function coupons()
    {
        return $this->hasMany(Coupon::class);
    }

    public function sections()
    {
        return $this->hasMany(Section::class);
    }

    public function lessons()
    {
        return $this->hasMany(Lesson::class);
    }

    public function enrollments()
    {
        return $this->hasMany(Enrollments::class);
    }

    public function wishlists()
    {
        $query = $this->hasMany(Wishlist::class);

        if (auth()->user()) {
            $query->where('user_id', auth()->user()->id);
        }

        return $query;
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function instructors($instructors_ids = array())
    {
        if (!is_array($instructors_ids)) {
            $instructors_ids = $instructors_ids ? json_decode($instructors_ids, true) : [];
        } elseif (is_array($instructors_ids) && count($instructors_ids) == 0) {
            $instructors_ids = $this->instructor_ids ? json_decode($this->instructor_ids, true) : [];
        } else {
            $instructors_ids = array();
        }

        return User::whereIn('id', $instructors_ids)->get();
    }

    function total_second()
    {
        return $this->hasMany(Lesson::class)
            ->select('id')
            ->selectRaw('SUM(TIME_TO_SEC(duration)) as total_time')
            ->groupBy('id')
            ->first()->total_time;
    }

    function total_duration()
    {
        $total_seconds = $this->hasMany(Lesson::class)
            ->select('id')
            ->selectRaw('SUM(TIME_TO_SEC(duration)) as total_time')
            ->groupBy('id')
            ->first()->total_time;


        $hours = floor($total_seconds / 3600); // Calculate the number of hours
        $minutes = floor(($total_seconds / 60) % 60); // Calculate the number of minutes
        $total_seconds = $total_seconds % 60; // Calculate the remaining seconds

        $duration = sprintf("%02d:%02d:%02d", $hours, $minutes, $total_seconds);
        return $duration;
    }


    public function getCurrentPrice()
    {
        $priceTrends = $this->getPriceTrendsData();

        if (empty($priceTrends)) {
            return $this->price;
        }

        $today = now()->format('Y-m-d');
        $currentPrice = $this->price;
        $latestDate = null;

        foreach ($priceTrends as $trend) {
            $startDate = $trend['start_date'];

            // Nếu ngày bắt đầu đã qua và là ngày gần nhất so với hiện tại
            if ($startDate <= $today && ($latestDate === null || $startDate > $latestDate)) {
                $currentPrice = $trend['price'];
                $latestDate = $startDate;
            }
        }

        return $currentPrice;
    }

    public function getSortedPriceTrends()
    {
        $trends = $this->getPriceTrendsData();

        if (empty($trends)) {
            return [];
        }
        usort($trends, function ($a, $b) {
            return strtotime($a['start_date']) - strtotime($b['start_date']);
        });

        return $trends;
    }

    public function updatePriceBasedOnTrend()
    {
        $priceTrends = $this->getPriceTrendsData();

        // Nếu không có price trends, không cần xử lý
        if (empty($priceTrends)) {
            return false;
        }

        $today = Carbon::now()->format('Y-m-d');
        $needsUpdate = false;
        $newPrice = $this->price;

        // Sắp xếp price trends theo ngày (từ mới nhất đến cũ nhất)
        $trends = collect($priceTrends)->sortByDesc('start_date')->values()->all();

        foreach ($trends as $trend) {
            // Nếu ngày bắt đầu đã qua hoặc là hôm nay
            if ($trend['start_date'] <= $today) {
                // Nếu giá hiện tại khác với giá trong trend
                if ($this->price != $trend['price']) {
                    $newPrice = $trend['price'];
                    $needsUpdate = true;
                }
                break; // Lấy trend mới nhất mà đã qua ngày bắt đầu
            }
        }

        // Cập nhật giá nếu cần
        if ($needsUpdate) {
            $this->price = $newPrice;
            $this->save();
            return true;
        }

        return false;
    }

    /**
     * Kiểm tra xem khóa học có sử dụng multiple pricing plans không
     */
    public function hasMultiplePricingPlans()
    {
        return $this->pricing_type === 'multiple_plans' && !empty($this->pricing_plans);
    }

    /**
     * Lấy danh sách pricing plans đã được sắp xếp
     */
    public function getPricingPlans()
    {
        if (!$this->hasMultiplePricingPlans()) {
            return [];
        }

        $plans = $this->getPricingPlansData();

        // Sắp xếp theo thứ tự: lifetime cuối, các gói khác theo duration tăng dần
        usort($plans, function($a, $b) {
            if ($a['duration_type'] === 'lifetime' && $b['duration_type'] !== 'lifetime') {
                return 1; // lifetime xuống cuối
            }
            if ($a['duration_type'] !== 'lifetime' && $b['duration_type'] === 'lifetime') {
                return -1; // lifetime xuống cuối
            }
            if ($a['duration_type'] === 'lifetime' && $b['duration_type'] === 'lifetime') {
                return 0; // cả hai đều lifetime
            }

            // So sánh theo duration_value
            return ($a['duration_value'] ?? 0) - ($b['duration_value'] ?? 0);
        });

        return $plans;
    }

    /**
     * Lấy plan theo ID
     */
    public function getPricingPlanById($planId)
    {
        $plans = $this->getPricingPlans();

        foreach ($plans as $plan) {
            if ($plan['id'] == $planId) {
                return $plan;
            }
        }

        return null;
    }

    /**
     * Lấy giá hiện tại của plan (có tính discount)
     */
    public function getPlanCurrentPrice($planId)
    {
        $plan = $this->getPricingPlanById($planId);

        if (!$plan) {
            return 0;
        }

        // Nếu có giá khuyến mãi và khuyến mãi được bật
        if ($this->discount_flag == 1 && isset($plan['discounted_price']) && $plan['discounted_price'] > 0) {
            return $plan['discounted_price'];
        }

        return $plan['price'];
    }

    /**
     * Lấy plan phổ biến nhất
     */
    public function getPopularPlan()
    {
        $plans = $this->getPricingPlans();

        foreach ($plans as $plan) {
            if (isset($plan['is_popular']) && $plan['is_popular']) {
                return $plan;
            }
        }

        // Nếu không có plan nào được đánh dấu popular, trả về plan đầu tiên
        return !empty($plans) ? $plans[0] : null;
    }

    /**
     * Tính toán ngày hết hạn dựa trên plan
     */
    public function calculateExpiryDate($planId)
    {
        $plan = $this->getPricingPlanById($planId);

        if (!$plan) {
            return null;
        }

        if ($plan['duration_type'] === 'lifetime') {
            return null; // Không hết hạn
        }

        if ($plan['duration_type'] === 'months' && isset($plan['duration_value'])) {
            $days = $plan['duration_value'] * 30;
            return strtotime("+" . $days . " days");
        }

        return null;
    }

}
