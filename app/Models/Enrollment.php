<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Enrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'pricing_plan_id',
        'paid_amount',
        'enrollment_type',
        'entry_date',
        'expiry_date',
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function users()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Lấy thông tin pricing plan đã mua
     */
    public function getPricingPlan()
    {
        if (!$this->pricing_plan_id || !$this->course) {
            return null;
        }

        return $this->course->getPricingPlanById($this->pricing_plan_id);
    }

    /**
     * Kiểm tra xem enrollment có sử dụng pricing plan không
     */
    public function hasPricingPlan()
    {
        return !is_null($this->pricing_plan_id);
    }

    /**
     * Tính toán ngày hết hạn dựa trên pricing plan
     */
    public function calculateExpiryFromPlan()
    {
        if (!$this->hasPricingPlan() || !$this->course) {
            return null;
        }

        return $this->course->calculateExpiryDate($this->pricing_plan_id);
    }

    /**
     * Lấy tên gói đã mua
     */
    public function getPlanName()
    {
        $plan = $this->getPricingPlan();
        return $plan ? $plan['name'] : 'Gói cơ bản';
    }

    /**
     * Kiểm tra enrollment có còn hiệu lực không
     */
    public function isActive()
    {
        if (is_null($this->expiry_date)) {
            return true; // Không hết hạn
        }

        return $this->expiry_date > time();
    }
}
