<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->string('pricing_type')->default('single')->after('price_trends')->comment('single hoặc multiple_plans');
            $table->json('pricing_plans')->nullable()->after('pricing_type')->comment('C<PERSON>u hình các gói giá khác nhau');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn(['pricing_type', 'pricing_plans']);
        });
    }
};
