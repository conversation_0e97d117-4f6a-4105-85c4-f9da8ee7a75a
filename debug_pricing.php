<?php

// Debug script để kiểm tra pricing system
require_once 'vendor/autoload.php';

use App\Models\Course;

// Khởi tạo Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Debug Pricing System ===\n\n";

// Kiểm tra course có pricing_plans không
$courses = Course::whereNotNull('pricing_plans')->get();
echo "Courses with pricing_plans: " . $courses->count() . "\n";

foreach ($courses as $course) {
    echo "\nCourse ID: {$course->id}\n";
    echo "Title: {$course->title}\n";
    echo "Pricing Type: " . ($course->pricing_type ?? 'null') . "\n";
    
    try {
        $plans = $course->getPricingPlansData();
        echo "Plans count: " . count($plans) . "\n";
        
        if ($course->hasMultiplePricingPlans()) {
            echo "✓ Has multiple pricing plans\n";
            $sortedPlans = $course->getPricingPlans();
            foreach ($sortedPlans as $plan) {
                echo "  - Plan {$plan['id']}: {$plan['name']}\n";
            }
        } else {
            echo "✗ No multiple pricing plans\n";
        }
    } catch (Exception $e) {
        echo "✗ Error: " . $e->getMessage() . "\n";
    }
}

// Kiểm tra course có price_trends không
echo "\n--- Price Trends Check ---\n";
$coursesWithTrends = Course::whereNotNull('price_trends')->get();
echo "Courses with price_trends: " . $coursesWithTrends->count() . "\n";

foreach ($coursesWithTrends as $course) {
    echo "\nCourse ID: {$course->id}\n";
    try {
        $trends = $course->getPriceTrendsData();
        echo "Trends count: " . count($trends) . "\n";
        
        $currentPrice = $course->getCurrentPrice();
        echo "Current price: " . number_format($currentPrice) . "\n";
    } catch (Exception $e) {
        echo "✗ Error: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Debug completed ===\n";
