@php
    $courses = App\Models\Course::pluck('title', 'id')->toArray();
    $aff_users = App\Models\User::where('role', 'student')->pluck('email', 'id')->toArray();
@endphp

<script>
    $('.ol-select2').select2({
        dropdownParent: $("#ajaxModal")
    });

    // Add JavaScript to check coupon code existence
    $(document).ready(function() {
        $('#code').on('input', function() {
            var code = $(this).val().trim();
            if (code.length > 2) {
                checkCouponExists(code);
            } else {
                $('#coupon-message').html('').hide();
            }
        });

        function checkCouponExists(code) {
            $.ajax({
                url: "{{ route('admin.coupon.check-exists') }}",
                type: "POST",
                data: {
                    code: code,
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.exists) {
                        $('#coupon-message').html('Mã coupon đã tồn tại').addClass('text-danger').removeClass('text-success').show();
                    } else {
                        $('#coupon-message').html('Mã coupon có thể sử dụng').addClass('text-success').removeClass('text-danger').show();
                    }
                },
                error: function() {
                    $('#coupon-message').html('').hide();
                }
            });
        }

        // Xử lý sự kiện click nút tạo mã tự động
        $('#generate-code-btn').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();

            // Hiển thị loading
            $btn.html('<i class="fi-rr-spinner"></i> Đang tạo...').prop('disabled', true);

            $.ajax({
                url: "{{ route('admin.coupon.generate-code') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.success) {
                        $('#code').val(response.code);
                        $('#coupon-message').html('Mã coupon đã được tạo thành công: ' + response.code).addClass('text-success').removeClass('text-danger').show();
                    } else {
                        $('#coupon-message').html(response.message).addClass('text-danger').removeClass('text-success').show();
                    }
                },
                error: function() {
                    $('#coupon-message').html('Có lỗi xảy ra khi tạo mã coupon').addClass('text-danger').removeClass('text-success').show();
                },
                complete: function() {
                    // Khôi phục nút về trạng thái ban đầu
                    $btn.html(originalText).prop('disabled', false);
                }
            });
        });
    });
</script>
<div class="ol-card">
    <div class="ol-card-body">
        <form action="{{ route('admin.coupon.store') }}" method="post" enctype="multipart/form-data">
            @csrf
            <div class="row">
                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label" for="code">Mã coupon</label>
                    <div class="input-group">
                        <input type="text" class="form-control ol-form-control" name="code" id="code"
                            placeholder="Nhập mã coupon" required>
                        <button type="button" class="btn btn-outline-primary" id="generate-code-btn">
                            <i class="fi-rr-refresh"></i> Tạo mã tự động
                        </button>
                    </div>
                    <div id="coupon-message" class="mt-1" style="display: none;"></div>
                </div>

                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label"
                        for="user_aff_id">{{ get_phrase('Choose User Affiliate') }}</label>
                    <select for='user_aff_id' class="form-control ol-form-control ol-select2" name="user_aff_id"
                        id="user_aff_id">
                        <option value="">{{ get_phrase('Choose user affiliate ...') }}</option>
                        @foreach ($aff_users as $id => $title)
                            <option value="{{ $id }}">{{ $title }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label" for="course_id">{{ get_phrase('Choose Course') }}</label>
                    <select for='course_id' class="form-control ol-form-control ol-select2" name="course_id"
                        id="course_id" required>
                        <option value="">{{ get_phrase('Choose course ...') }}</option>
                        @foreach ($courses as $id => $title)
                            <option value="{{ $id }}">{{ $title }}</option>
                        @endforeach
                    </select>
                </div>

                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label" for="quantity">{{ get_phrase('Quantity') }}</label>
                    <input type="number" class="form-control ol-form-control" name="quantity" id="quantity"
                        placeholder="{{ get_phrase('Enter coupon quantity') }}">
                </div>
                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label" for="discount">{{ get_phrase('Discount (%)') }}</label>
                    <input type="number" max="100" min="0" class="form-control ol-form-control" name="discount"
                        id="discount" placeholder="{{ get_phrase('Enter coupon discount') }}" required>
                </div>

                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label" for="expiry">{{ get_phrase('Expiry') }}</label>
                    <input type="date" class="form-control ol-form-control" name="expiry" id="expiry"
                        placeholder="{{ get_phrase('Enter coupon expiry') }}" required>
                </div>

                <div class="fpb-7 mb-3">
                    <label class="form-label ol-form-label" for="status">{{ get_phrase('Status') }}</label>
                    <select for='status' class="form-control ol-form-control ol-select2" name="status" id="status"
                        required>
                        <option value="">{{ get_phrase('Choose status ...') }}</option>
                        <option value="1">{{ get_phrase('Active') }}</option>
                        <option value="2">{{ get_phrase('Private') }}</option>
                        <option value="0">{{ get_phrase('Inactive') }}</option>
                    </select>
                </div>
            </div>

            <div class="fpb-7 mb-2 d-flex justify-content-end">
                <button type="submit" class="ol-btn-primary">{{ get_phrase('Add coupon') }}</button>
            </div>
        </form>
    </div>
</div>
