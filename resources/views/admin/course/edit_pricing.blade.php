<!-- Modern Pricing Configuration -->
<style>
.pricing-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.pricing-type-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.pricing-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.pricing-card.active::before {
    transform: scaleX(1);
}

.pricing-card.active {
    border-color: #28a745;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
}

.pricing-card:hover {
    border-color: #28a745;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.pricing-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
    color: white;
}

.pricing-card.free .pricing-card-icon {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
}

.pricing-card.paid .pricing-card-icon {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.pricing-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.pricing-card-description {
    font-size: 14px;
    color: #718096;
    line-height: 1.5;
}

.pricing-card input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
}

.section-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 16px;
    font-size: 18px;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.section-subtitle {
    font-size: 14px;
    color: #718096;
    margin: 4px 0 0 0;
}

/* Enhanced Form Styling */
.form-label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Success States */
.pricing-card.active .pricing-card-icon {
    transform: scale(1.1);
}

.pricing-card.active .pricing-card-title {
    color: #28a745;
}

/* Loading States */
.pricing-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.pricing-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid #28a745;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .pricing-type-cards {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .pricing-container {
        padding: 20px;
    }

    .section-header {
        flex-direction: column;
        text-align: center;
    }

    .section-icon {
        margin-right: 0;
        margin-bottom: 12px;
    }

    .timeline-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .timeline-badge {
        align-self: flex-start;
    }

    .price-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

@media (max-width: 576px) {
    .pricing-card {
        padding: 16px;
    }

    .pricing-card-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .section-title {
        font-size: 18px;
    }

    .timeline-row {
        padding: 16px;
    }
}
</style>

<div class="pricing-container">
    <div class="section-header">
        <div class="section-icon">
            <i class="fi-rr-dollar"></i>
        </div>
        <div>
            <h3 class="section-title">Cấu hình giá khóa học</h3>
            <p class="section-subtitle">Chọn loại giá và thiết lập chi tiết cho khóa học của bạn</p>
        </div>
    </div>

    <!-- Optimized Single Column Workflow -->
    <div class="pricing-workflow">
        <style>
        .pricing-workflow {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 20px; /* Thêm padding để tránh sát mép */
        }

        /* Đảm bảo tất cả workflow steps có cùng alignment */
        .pricing-workflow > .workflow-step,
        .pricing-workflow > .workflow-connector {
            width: 100%;
            box-sizing: border-box;
        }

        .workflow-step {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 24px;
            border: 2px solid #e9ecef;
            position: relative;
            transition: all 0.3s ease;
        }

        .workflow-step.active {
            border-color: #28a745;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
        }

        /* Smooth transitions for dynamic content */
        .number-of-month {
            transition: all 0.4s ease;
            overflow: hidden;
        }

        .number-of-month.d-hidden {
            max-height: 0;
            opacity: 0;
            margin-top: 0 !important;
            padding-top: 0;
            padding-bottom: 0;
        }

        .number-of-month:not(.d-hidden) {
            max-height: 500px;
            opacity: 1;
        }

        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .step-number {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 20px;
            font-size: 18px;
        }

        .step-number.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .step-info {
            flex: 1;
        }

        .step-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 4px 0;
        }

        .step-subtitle {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }

        .step-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            align-items: stretch; /* Đảm bảo các card có chiều cao bằng nhau */
        }

        .option-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            min-height: 200px; /* Đảm bảo chiều cao tối thiểu */
        }

        .option-card.selected {
            background: #e8f5e8;
            border-color: #28a745;
        }

        .option-card:hover {
            border-color: #28a745;
            background: #f0f8f0;
            transform: translateY(-2px);
        }

        .option-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 24px;
            color: white;
            flex-shrink: 0; /* Không cho phép icon co lại */
        }

        .option-icon.free {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
        }

        .option-icon.paid {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .option-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
            flex-shrink: 0; /* Không cho phép title co lại */
        }

        .option-description {
            font-size: 14px;
            color: #718096;
            line-height: 1.5;
            flex-grow: 1; /* Cho phép description mở rộng để fill không gian */
            display: flex;
            align-items: center;
            text-align: center;
        }

        .workflow-connector {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            position: relative;
            z-index: 1;
        }

        .connector-line {
            width: 4px;
            height: 30px;
            background: linear-gradient(to bottom, #28a745, #20c997);
            border-radius: 2px;
            position: relative;
        }

        .connector-line::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 8px solid #20c997;
        }

        @media (max-width: 768px) {
            .step-options {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .option-card {
                min-height: 160px; /* Giảm chiều cao tối thiểu trên mobile */
                padding: 20px;
            }

            .workflow-step {
                padding: 20px;
                margin-bottom: 16px;
            }

            .step-header {
                flex-direction: row; /* Giữ nguyên layout ngang trên mobile */
                text-align: left;
                align-items: center;
            }

            .step-number {
                margin-right: 16px;
                margin-bottom: 0;
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .step-title {
                font-size: 18px;
            }

            .step-subtitle {
                font-size: 13px;
            }

            .option-title {
                font-size: 16px;
            }

            .option-description {
                font-size: 13px;
            }

            .workflow-connector {
                margin: 12px 0;
            }

            .connector-line {
                height: 20px;
            }
        }
        </style>

        <!-- Step 1: Choose Pricing Type -->
        <div class="workflow-step {{ $course_details->is_paid != 1 || $course_details->is_paid == 1 ? 'active' : '' }}">
            <div class="step-header">
                <div class="step-number active">1</div>
                <div class="step-info">
                    <h3 class="step-title">Chọn loại giá khóa học</h3>
                    <p class="step-subtitle">Quyết định khóa học của bạn sẽ miễn phí hay có phí</p>
                </div>
            </div>

            <div class="step-options">
                <div class="option-card {{ $course_details->is_paid != 1 ? 'selected' : '' }}" onclick="selectPricingType('free')">
                    <input type="radio" name="is_paid" value="0" id="free" {{ $course_details->is_paid != 1 ? 'checked' : '' }} style="display: none;">
                    <div class="option-icon free">
                        <i class="fi-rr-gift"></i>
                    </div>
                    <div class="option-title">Khóa học miễn phí</div>
                    <div class="option-description">
                        Khóa học hoàn toàn miễn phí, học viên có thể truy cập mà không cần thanh toán
                    </div>
                </div>

                <div class="option-card {{ $course_details->is_paid == 1 ? 'selected' : '' }}" onclick="selectPricingType('paid')">
                    <input type="radio" name="is_paid" value="1" id="paid" {{ $course_details->is_paid == 1 ? 'checked' : '' }} style="display: none;">
                    <div class="option-icon paid">
                        <i class="fi-rr-credit-card"></i>
                    </div>
                    <div class="option-title">Khóa học có phí</div>
                    <div class="option-description">
                        Khóa học trả phí với nhiều tùy chọn giá và chương trình khuyến mãi linh hoạt
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 1.5: Choose Pricing Strategy (Only show when paid is selected) -->
        <div class="paid-section @if($course_details->is_paid != 1) d-hidden @endif" id="pricing-strategy-section">
            <div class="workflow-step active">
                <div class="step-header">
                    <div class="step-number active">1.5</div>
                    <div class="step-info">
                        <h3 class="step-title">Chọn chiến lược giá</h3>
                        <p class="step-subtitle">Quyết định sử dụng giá đơn giản hay nhiều gói giá linh hoạt</p>
                    </div>
                </div>

                <div class="step-options">
                    <div class="option-card {{ ($course_details->pricing_type ?? 'single') == 'single' ? 'selected' : '' }}" onclick="selectPricingStrategy('single')">
                        <input type="radio" name="pricing_type" value="single" id="single_pricing" {{ ($course_details->pricing_type ?? 'single') == 'single' ? 'checked' : '' }} style="display: none;">
                        <div class="option-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);">
                            <i class="fi-rr-dollar"></i>
                        </div>
                        <div class="option-title">Giá đơn giản</div>
                        <div class="option-description">
                            Một mức giá duy nhất cho khóa học, phù hợp với hầu hết các trường hợp
                        </div>
                    </div>

                    <div class="option-card {{ ($course_details->pricing_type ?? 'single') == 'multiple_plans' ? 'selected' : '' }}" onclick="selectPricingStrategy('multiple_plans')">
                        <input type="radio" name="pricing_type" value="multiple_plans" id="multiple_pricing" {{ ($course_details->pricing_type ?? 'single') == 'multiple_plans' ? 'checked' : '' }} style="display: none;">
                        <div class="option-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                            <i class="fi-rr-tags"></i>
                        </div>
                        <div class="option-title">Nhiều gói giá</div>
                        <div class="option-description">
                            Tạo nhiều gói giá khác nhau (1 tháng, 6 tháng, trọn đời...) để tăng doanh thu
                        </div>
                    </div>
                </div>
            </div>
        </div>

{{--        <!-- Workflow Connector -->--}}
{{--        <div class="workflow-connector">--}}
{{--            <div class="connector-line"></div>--}}
{{--        </div>--}}
    </div>
    <!-- Step 2: Multiple Pricing Plans Configuration -->
    <div class="paid-section @if($course_details->is_paid != 1 || ($course_details->pricing_type ?? 'single') != 'multiple_plans') d-hidden @endif" id="multiple-plans-section">
        <div class="pricing-workflow">
            <div class="workflow-step active">
                <div class="step-header">
                    <div class="step-number active">2A</div>
                    <div class="step-info">
                        <h3 class="step-title">Cấu hình các gói giá</h3>
                        <p class="step-subtitle">Tạo và quản lý các gói giá khác nhau cho khóa học</p>
                    </div>
                </div>

                <style>
                    .pricing-plans-container {
                        background: white;
                        border-radius: 12px;
                        padding: 24px;
                        border: 2px solid #e9ecef;
                    }

                    .plan-item {
                        background: #f8f9fa;
                        border: 2px solid #e9ecef;
                        border-radius: 12px;
                        padding: 20px;
                        margin-bottom: 16px;
                        position: relative;
                        transition: all 0.3s ease;
                    }

                    .plan-item:hover {
                        border-color: #28a745;
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    }

                    .plan-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 16px;
                    }

                    .plan-title {
                        font-size: 16px;
                        font-weight: 600;
                        color: #2d3748;
                        display: flex;
                        align-items: center;
                    }

                    .plan-title i {
                        margin-right: 8px;
                        color: #28a745;
                    }

                    .plan-actions {
                        display: flex;
                        gap: 8px;
                    }

                    .plan-btn {
                        padding: 6px 12px;
                        border: none;
                        border-radius: 6px;
                        font-size: 12px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }

                    .plan-btn.popular {
                        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
                        color: white;
                    }

                    .plan-btn.remove {
                        background: #dc3545;
                        color: white;
                    }

                    .plan-btn.remove:hover {
                        background: #c82333;
                    }

                    .plan-fields {
                        display: grid;
                        grid-template-columns: 1fr 1fr 1fr 1fr;
                        gap: 16px;
                        align-items: end;
                    }

                    .plan-field {
                        display: flex;
                        flex-direction: column;
                    }

                    .plan-field label {
                        font-size: 13px;
                        font-weight: 600;
                        color: #2d3748;
                        margin-bottom: 6px;
                    }

                    .plan-field input,
                    .plan-field select {
                        height: 40px;
                        border: 2px solid #e9ecef;
                        border-radius: 8px;
                        padding: 8px 12px;
                        font-size: 14px;
                        transition: all 0.3s ease;
                    }

                    .plan-field input:focus,
                    .plan-field select:focus {
                        border-color: #28a745;
                        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
                        outline: none;
                    }

                    .add-plan-btn {
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        margin-top: 16px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }

                    .add-plan-btn:hover {
                        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                        transform: translateY(-2px);
                    }

                    .popular-badge {
                        position: absolute;
                        top: -8px;
                        right: 16px;
                        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
                        color: white;
                        padding: 4px 12px;
                        border-radius: 12px;
                        font-size: 11px;
                        font-weight: 600;
                    }

                    @media (max-width: 768px) {
                        .plan-fields {
                            grid-template-columns: 1fr;
                            gap: 12px;
                        }
                    }
                </style>

                <div class="pricing-plans-container">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fi-rr-tags"></i>
                        </div>
                        <div>
                            <h4 class="section-title">Quản lý gói giá</h4>
                            <p class="section-subtitle">Tạo các gói giá khác nhau để tối ưu hóa doanh thu</p>
                        </div>
                    </div>

                    <div id="pricing-plans-list">
                        @php
                            $existingPlans = $course_details->pricing_plans ?? [];
                            if (empty($existingPlans)) {
                                // Tạo 3 gói mặc định nếu chưa có
                                $existingPlans = [
                                    [
                                        'id' => 1,
                                        'name' => 'Gói 1 tháng',
                                        'duration_type' => 'months',
                                        'duration_value' => 1,
                                        'price' => '',
                                        'discounted_price' => '',
                                        'is_popular' => false
                                    ],
                                    [
                                        'id' => 2,
                                        'name' => 'Gói 6 tháng',
                                        'duration_type' => 'months',
                                        'duration_value' => 6,
                                        'price' => '',
                                        'discounted_price' => '',
                                        'is_popular' => true
                                    ],
                                    [
                                        'id' => 3,
                                        'name' => 'Trọn đời',
                                        'duration_type' => 'lifetime',
                                        'duration_value' => null,
                                        'price' => '',
                                        'discounted_price' => '',
                                        'is_popular' => false
                                    ]
                                ];
                            }
                        @endphp

                        @foreach($existingPlans as $index => $plan)
                        <div class="plan-item" data-plan-id="{{ $plan['id'] }}">
                            @if($plan['is_popular'] ?? false)
                            <div class="popular-badge">Phổ biến</div>
                            @endif

                            <div class="plan-header">
                                <div class="plan-title">
                                    <i class="fi-rr-package"></i>
                                    Gói giá #{{ $plan['id'] }}
                                </div>
                                <div class="plan-actions">
                                    <button type="button" class="plan-btn popular" onclick="togglePopular({{ $plan['id'] }})"
                                            id="popular-btn-{{ $plan['id'] }}"
                                            style="{{ ($plan['is_popular'] ?? false) ? '' : 'background: #6c757d;' }}">
                                        {{ ($plan['is_popular'] ?? false) ? 'Phổ biến' : 'Đặt phổ biến' }}
                                    </button>
                                    <button type="button" class="plan-btn remove" onclick="removePlan({{ $plan['id'] }})">
                                        <i class="fi-rr-trash"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="plan-fields">
                                <div class="plan-field">
                                    <label>Tên gói</label>
                                    <input type="text" name="plans[{{ $plan['id'] }}][name]"
                                           value="{{ $plan['name'] }}"
                                           placeholder="VD: Gói 1 tháng">
                                </div>

                                <div class="plan-field">
                                    <label>Loại thời hạn</label>
                                    <select name="plans[{{ $plan['id'] }}][duration_type]"
                                            onchange="updateDurationField({{ $plan['id'] }}, this.value)">
                                        <option value="months" {{ ($plan['duration_type'] ?? '') == 'months' ? 'selected' : '' }}>Theo tháng</option>
                                        <option value="lifetime" {{ ($plan['duration_type'] ?? '') == 'lifetime' ? 'selected' : '' }}>Trọn đời</option>
                                    </select>
                                </div>

                                <div class="plan-field" id="duration-field-{{ $plan['id'] }}"
                                     style="{{ ($plan['duration_type'] ?? '') == 'lifetime' ? 'display: none;' : '' }}">
                                    <label>Số tháng</label>
                                    <input type="number" name="plans[{{ $plan['id'] }}][duration_value]"
                                           value="{{ $plan['duration_value'] ?? '' }}"
                                           min="1" max="120" placeholder="VD: 6">
                                </div>

                                <div class="plan-field">
                                    <label>Giá gốc ({{ currency() }})</label>
                                    <input type="number" name="plans[{{ $plan['id'] }}][price]"
                                           value="{{ $plan['price'] ?? '' }}"
                                           min="0" step="1000" placeholder="VD: 299000">
                                </div>
                            </div>

                            <div class="plan-fields" style="margin-top: 12px;">
                                <div class="plan-field">
                                    <label>Giá khuyến mãi ({{ currency() }}) - Tùy chọn</label>
                                    <input type="number" name="plans[{{ $plan['id'] }}][discounted_price]"
                                           value="{{ $plan['discounted_price'] ?? '' }}"
                                           min="0" step="1000" placeholder="VD: 199000">
                                </div>

                                <div class="plan-field">
                                    <input type="hidden" name="plans[{{ $plan['id'] }}][id]" value="{{ $plan['id'] }}">
                                    <input type="hidden" name="plans[{{ $plan['id'] }}][is_popular]"
                                           value="{{ ($plan['is_popular'] ?? false) ? '1' : '0' }}"
                                           id="popular-input-{{ $plan['id'] }}">
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <button type="button" class="add-plan-btn" onclick="addNewPlan()">
                        <i class="fi-rr-plus"></i>
                        Thêm gói giá mới
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Single Pricing Configuration -->
    <div class="paid-section @if($course_details->is_paid != 1 || ($course_details->pricing_type ?? 'single') != 'single') d-hidden @endif" id="paid-section">
        <div class="pricing-workflow">
            <div class="workflow-step active">
                <div class="step-header">
                    <div class="step-number active">2</div>
                    <div class="step-info">
                        <h3 class="step-title">Thiết lập giá khóa học</h3>
                        <p class="step-subtitle">Cấu hình giá gốc và các chương trình khuyến mãi</p>
                    </div>
                </div>

                <style>
                    .paid-configuration {
                        background: transparent;
                        border-radius: 0;
                        padding: 0;
                        box-shadow: none;
                        border: none;
                        margin-top: 0;
                    }

                    .price-input-group {
                        position: relative;
                        margin-bottom: 24px;
                    }

                    .price-input-wrapper {
                        position: relative;
                        display: flex;
                        align-items: center;
                    }

                    .currency-symbol {
                        position: absolute;
                        left: 16px;
                        top: 50%;
                        transform: translateY(-50%);
                        font-weight: 600;
                        color: #495057;
                        z-index: 2;
                        font-size: 16px;
                    }

                    .price-input {
                        padding-left: 45px !important;
                        font-size: 18px;
                        font-weight: 600;
                        height: 56px;
                        border: 2px solid #e9ecef;
                        border-radius: 12px;
                        transition: all 0.3s ease;
                        background: #f8f9fa;
                    }

                    .price-input:focus {
                        border-color: #28a745;
                        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
                        background: white;
                    }

                    /* Hide number input spinners */
                    .price-input::-webkit-outer-spin-button,
                    .price-input::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }

                    .price-input[type=number] {
                        -moz-appearance: textfield;
                    }

                    .price-timeline-input::-webkit-outer-spin-button,
                    .price-timeline-input::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }

                    .price-timeline-input[type=number] {
                        -moz-appearance: textfield;
                    }

                    .price-label {
                        font-size: 16px;
                        font-weight: 600;
                        color: #2d3748;
                        margin-bottom: 8px;
                        display: flex;
                        align-items: center;
                    }

                    .price-label i {
                        margin-right: 8px;
                        color: #28a745;
                    }

                    .price-helper {
                        font-size: 13px;
                        color: #718096;
                        margin-top: 6px;
                    }

                    .discount-section {
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        border-radius: 12px;
                        padding: 24px;
                        margin-top: 30px;
                        border: 1px solid #dee2e6;
                    }

                    .discount-toggle {
                        display: flex;
                        align-items: center;
                        margin-bottom: 20px;
                    }

                    .toggle-switch {
                        position: relative;
                        width: 50px;
                        height: 24px;
                        background: #ccc;
                        border-radius: 12px;
                        cursor: pointer;
                        transition: background 0.3s ease;
                        margin-right: 12px;
                    }

                    .toggle-switch.active {
                        background: #28a745;
                    }

                    .toggle-switch::before {
                        content: '';
                        position: absolute;
                        width: 20px;
                        height: 20px;
                        background: white;
                        border-radius: 50%;
                        top: 2px;
                        left: 2px;
                        transition: transform 0.3s ease;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                    }

                    .toggle-switch.active::before {
                        transform: translateX(26px);
                    }

                    .discount-label {
                        font-size: 16px;
                        font-weight: 600;
                        color: #2d3748;
                    }
                </style>

                <div class="paid-configuration">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fi-rr-tags"></i>
                        </div>
                        <div>
                            <h4 class="section-title">Thiết lập giá khóa học</h4>
                            <p class="section-subtitle">Cấu hình giá gốc và các chương trình khuyến mãi</p>
                        </div>
                    </div>

                    <!-- Base Price -->
                    <div class="price-input-group">
                        <label class="price-label">
                            <i class="fi-rr-dollar"></i>
                            Giá gốc khóa học <span class="text-danger">*</span>
                        </label>
                        <div class="price-input-wrapper">
                            <span class="currency-symbol">{{ currency() }}</span>
                            <input type="number"
                                   name="price"
                                   class="form-control price-input"
                                   placeholder="Nhập giá khóa học"
                                   value="{{ $course_details->price }}"
                                   min="0"
                                   step="1"
                                   id="price"
                                   required>
                        </div>
                        <div class="price-helper">
                            <i class="fi-rr-info"></i> Giá này sẽ hiển thị cho học viên khi chưa có khuyến mãi
                        </div>
                    </div>

                    <!-- Discount Section -->
                    <div class="discount-section">
                        <div class="discount-toggle">
                            <div class="toggle-switch {{ $course_details->discount_flag == 1 ? 'active' : '' }}"
                                 onclick="toggleDiscount()" id="discount-toggle">
                            </div>
                            <label class="discount-label">Kích hoạt chương trình khuyến mãi</label>
                            <input type="checkbox"
                                   name="discount_flag"
                                   value="1"
                                   id="discount_flag"
                                   style="display: none;"
                                   onchange="togglePriceTrend()"
                                {{ $course_details->discount_flag == 1 ? 'checked' : '' }}>
                        </div>
                        <div class="price-helper">
                            <i class="fi-rr-info"></i> Bật tính năng này để tạo các mức giá khuyến mãi theo thời gian
                        </div>
                    </div>

                    <!-- Price Trend section - initially hidden unless discount_flag is checked -->
                    <div id="price-trend-section" style="@if($course_details->discount_flag != 1) display: none; @endif">
                        <style>
                            .price-trend-container {
                                background: white;
                                border-radius: 12px;
                                padding: 24px;
                                margin-top: 20px;
                                border: 2px solid #e9ecef;
                            }

                            .timeline-row {
                                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                                border-radius: 12px;
                                padding: 20px;
                                margin-bottom: 16px;
                                border: 1px solid #dee2e6;
                                position: relative;
                                transition: all 0.3s ease;
                            }

                            .timeline-row:hover {
                                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                            }

                            .timeline-row::before {
                                content: '';
                                position: absolute;
                                left: 0;
                                top: 0;
                                bottom: 0;
                                width: 4px;
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                border-radius: 2px;
                            }

                            .timeline-header {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                margin-bottom: 16px;
                            }

                            .timeline-title {
                                font-size: 16px;
                                font-weight: 600;
                                color: #2d3748;
                                display: flex;
                                align-items: center;
                            }

                            .timeline-title i {
                                margin-right: 8px;
                                color: #28a745;
                            }

                            .timeline-badge {
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                padding: 6px 12px;
                                border-radius: 20px;
                                font-size: 12px;
                                font-weight: 600;
                            }

                            .timeline-badge.first {
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                            }

                            .timeline-badge.second {
                                background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
                            }

                            .timeline-badge.third {
                                background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
                            }

                            .price-timeline-input {
                                height: 48px;
                                border: 2px solid #e9ecef;
                                border-radius: 8px;
                                font-weight: 600;
                                transition: all 0.3s ease;
                            }

                            .price-timeline-input:focus {
                                border-color: #28a745;
                                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
                            }

                            .timeline-info {
                                background: #e3f2fd;
                                border: 1px solid #bbdefb;
                                border-radius: 8px;
                                padding: 12px;
                                margin-top: 16px;
                                font-size: 13px;
                                color: #1565c0;
                            }

                            .add-timeline-btn {
                                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                                border: none;
                                color: white;
                                padding: 12px 24px;
                                border-radius: 8px;
                                font-weight: 600;
                                transition: all 0.3s ease;
                                margin-top: 16px;
                            }

                            .add-timeline-btn:hover {
                                box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
                            }
                        </style>

                        <div class="price-trend-container">
                            <div class="section-header">
                                <div class="section-icon">
                                    <i class="fi-rr-time-quarter-past"></i>
                                </div>
                                <div>
                                    <h4 class="section-title">Lịch trình giá khuyến mãi</h4>
                                    <p class="section-subtitle">Thiết lập các mức giá theo thời gian để tạo chiến lược marketing hiệu quả</p>
                                </div>
                            </div>

                            <div class="price-timeline">
                                @php
                                    // Lấy dữ liệu price_trends từ model khóa học và đảm bảo nó là mảng
                                    $priceTrends = [];

                                    // Kiểm tra xem price_trends có tồn tại và là mảng không
                                    if (isset($course_details->price_trends) && is_array($course_details->price_trends)) {
                                        $priceTrends = $course_details->price_trends;
                                    }

                                    // Kiểm tra nếu là chuỗi JSON thì decode thành mảng
                                    elseif (isset($course_details->price_trends) && is_string($course_details->price_trends)) {
                                        $decoded = json_decode($course_details->price_trends, true);
                                        if (is_array($decoded)) {
                                            $priceTrends = $decoded;
                                        }
                                    }
                                @endphp

                                    <!-- Timeline Row 1 -->
                                <div class="timeline-row" id="timeline-row-1">
                                    <div class="timeline-header">
                                        <div class="timeline-title">
                                            <i class="fi-rr-calendar"></i>
                                            Giai đoạn giá đầu tiên
                                        </div>
                                        <span class="timeline-badge first">Giai đoạn 1</span>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fi-rr-dollar"></i>
                                                    Giá khuyến mãi ({{ currency() }})
                                                </label>
                                                <input type="number" name="timeline_price[]"
                                                       value="{{ isset($priceTrends[0]['price']) ? $priceTrends[0]['price'] : '' }}"
                                                       class="form-control price-timeline-input" min="0"
                                                       step="1" placeholder="Nhập giá khuyến mãi">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fi-rr-calendar-day"></i>
                                                    Ngày bắt đầu
                                                </label>
                                                <input type="date" name="timeline_start_date[]"
                                                       value="{{ isset($priceTrends[0]['start_date']) ? $priceTrends[0]['start_date'] : '' }}"
                                                       class="form-control price-timeline-input timeline-date" id="start-date-1">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Timeline Row 2 -->
                                <div class="timeline-row" id="timeline-row-2">
                                    <div class="timeline-header">
                                        <div class="timeline-title">
                                            <i class="fi-rr-calendar"></i>
                                            Giai đoạn giá thứ hai
                                        </div>
                                        <span class="timeline-badge second">Giai đoạn 2</span>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fi-rr-dollar"></i>
                                                    Giá khuyến mãi ({{ currency() }})
                                                </label>
                                                <input type="number" name="timeline_price[]"
                                                       value="{{ isset($priceTrends[1]['price']) ? $priceTrends[1]['price'] : '' }}"
                                                       class="form-control price-timeline-input" min="0"
                                                       step="1" placeholder="Nhập giá khuyến mãi">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fi-rr-calendar-day"></i>
                                                    Ngày bắt đầu
                                                </label>
                                                <input type="date" name="timeline_start_date[]"
                                                       value="{{ isset($priceTrends[1]['start_date']) ? $priceTrends[1]['start_date'] : '' }}"
                                                       class="form-control price-timeline-input timeline-date" id="start-date-2"
                                                    {{ empty($priceTrends[0]['start_date']) ? 'disabled' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Timeline Row 3 -->
                                <div class="timeline-row" id="timeline-row-3">
                                    <div class="timeline-header">
                                        <div class="timeline-title">
                                            <i class="fi-rr-calendar"></i>
                                            Giai đoạn giá cuối cùng
                                        </div>
                                        <span class="timeline-badge third">Giai đoạn 3</span>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fi-rr-dollar"></i>
                                                    Giá khuyến mãi ({{ currency() }})
                                                </label>
                                                <input type="number" name="timeline_price[]"
                                                       value="{{ isset($priceTrends[2]['price']) ? $priceTrends[2]['price'] : '' }}"
                                                       class="form-control price-timeline-input" min="0"
                                                       step="1" placeholder="Nhập giá khuyến mãi">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fi-rr-calendar-day"></i>
                                                    Ngày bắt đầu
                                                </label>
                                                <input type="date" name="timeline_start_date[]"
                                                       value="{{ isset($priceTrends[2]['start_date']) ? $priceTrends[2]['start_date'] : '' }}"
                                                       class="form-control price-timeline-input timeline-date" id="start-date-3"
                                                    {{ empty($priceTrends[1]['start_date']) ? 'disabled' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="timeline-info">
                                    <i class="fi-rr-info"></i>
                                    <strong>Lưu ý:</strong> Mỗi ngày đại diện cho thời điểm bắt đầu của một giai đoạn giá.
                                    Ngày kết thúc của mỗi giai đoạn sẽ là ngày bắt đầu của giai đoạn tiếp theo.
                                    Giai đoạn cuối cùng sẽ kéo dài vô thời hạn.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Simple Discount Price (Alternative to Timeline) -->
            <div class="price-input-group" style="margin-top: 30px;">
                <label class="price-label">
                    <i class="fi-rr-percentage"></i>
                    Giá khuyến mãi đơn giản (tùy chọn)
                </label>
                <div class="price-input-wrapper">
                    <span class="currency-symbol">{{ currency() }}</span>
                    <input type="number"
                           name="discounted_price"
                           class="form-control price-input"
                           placeholder="Nhập giá khuyến mãi"
                           value="{{ $course_details->discounted_price }}"
                           min="0"
                           step="1"
                           id="discounted_price">
                </div>
                <div class="price-helper">
                    <i class="fi-rr-info"></i> Sử dụng tùy chọn này nếu bạn muốn áp dụng một mức giá khuyến mãi cố định thay vì lịch trình phức tạp
                </div>
            </div>
        </div>



        <!-- Step 3: Access Control -->
        <div class="pricing-workflow">
            <!-- Workflow Connector -->
            {{--    <div class="workflow-connector">--}}
            {{--        <div class="connector-line"></div>--}}
            {{--    </div>--}}

            <div class="workflow-step active">
                <div class="step-header">
                    <div class="step-number active">3</div>
                    <div class="step-info">
                        <h3 class="step-title">Thời hạn truy cập khóa học</h3>
                        <p class="step-subtitle">Thiết lập thời gian học viên có thể truy cập khóa học sau khi đăng ký/mua</p>
                    </div>
                </div>

                <div class="step-options">
                    <div class="option-card {{ !$course_details->expiry_period ? 'selected' : '' }}" onclick="selectExpiryType('lifetime')">
                        <input type="radio" name="expiry_period" value="lifetime" id="lifetime_expiry_period"
                               onchange="$('#number_of_month').slideUp(200)" {{ $course_details->expiry_period ? '' : 'checked' }} style="display: none;">
                        <div class="option-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <i class="fi-rr-infinity"></i>
                        </div>
                        <div class="option-title">Truy cập vĩnh viễn</div>
                        <div class="option-description">
                            Học viên có thể truy cập khóa học mãi mãi sau khi đăng ký/mua, không giới hạn thời gian
                        </div>
                    </div>

                    <div class="option-card {{ $course_details->expiry_period ? 'selected' : '' }}" onclick="selectExpiryType('limited')">
                        <input type="radio" name="expiry_period" value="limited_time" id="limited_expiry_period"
                               onchange="$('#number_of_month').slideDown(200)" {{ $course_details->expiry_period ? 'checked' : '' }} style="display: none;">
                        <div class="option-icon" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                            <i class="fi-rr-clock"></i>
                        </div>
                        <div class="option-title">Thời hạn có giới hạn</div>
                        <div class="option-description">
                            Học viên chỉ có thể truy cập khóa học trong một khoảng thời gian nhất định
                        </div>
                    </div>
                </div>

                <!-- Limited Time Configuration -->
                <div class="number-of-month {{ is_null($course_details->expiry_period) ? 'd-hidden' : '' }}" id="number_of_month" style="margin-top: 24px;">
                    <style>
                        .month-input-container {
                            background: #f8f9fa;
                            border: 2px solid #e9ecef;
                            border-radius: 12px;
                            padding: 20px;
                            margin-top: 16px;
                        }

                        .month-input-container.active {
                            border-color: #28a745;
                            background: #e8f5e8;
                        }

                        .month-input-header {
                            display: flex;
                            align-items: center;
                            margin-bottom: 16px;
                        }

                        .month-input-icon {
                            width: 40px;
                            height: 40px;
                            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
                            color: white;
                            border-radius: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 12px;
                            font-size: 16px;
                        }

                        .month-input-title {
                            font-size: 16px;
                            font-weight: 600;
                            color: #2d3748;
                            margin: 0;
                        }

                        .month-input-field {
                            position: relative;
                        }

                        .month-input-field input {
                            width: 100%;
                            padding: 12px 16px;
                            border: 2px solid #e9ecef;
                            border-radius: 8px;
                            font-size: 16px;
                            transition: all 0.3s ease;
                        }

                        .month-input-field input:focus {
                            border-color: #28a745;
                            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
                            outline: none;
                        }

                        .month-input-help {
                            margin-top: 12px;
                            font-size: 13px;
                            color: #718096;
                            display: flex;
                            align-items: center;
                        }

                        .month-input-help i {
                            margin-right: 8px;
                            color: #17a2b8;
                        }
                    </style>

                    <div class="month-input-container active">
                        <div class="month-input-header">
                            <div class="month-input-icon">
                                <i class="fi-rr-calendar-days"></i>
                            </div>
                            <h4 class="month-input-title">Cấu hình thời hạn truy cập</h4>
                        </div>

                        <div class="month-input-field">
                            <input class="form-control"
                                   type="number"
                                   name="number_of_month"
                                   min="1"
                                   max="120"
                                   value="{{ $course_details->expiry_period }}"
                                   placeholder="Nhập số tháng (VD: 6, 12, 24...)">
                        </div>

                        <div class="month-input-help">
                            <i class="fi-rr-info"></i>
                            Sau khi đăng ký/mua, học viên có thể truy cập khóa học trong số tháng bạn đã chọn
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>




<script>
    // Modern Workflow Interface JavaScript
    function selectPricingType(type) {
        // Remove selected class from all option cards
        document.querySelectorAll('.option-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Add selected class to clicked card
        const selectedCard = event.currentTarget;
        selectedCard.classList.add('selected');

        // Update radio button
        const radio = selectedCard.querySelector('input[type="radio"]');
        radio.checked = true;

        // Show/hide paid sections
        const pricingStrategySection = document.getElementById('pricing-strategy-section');
        const paidSection = document.getElementById('paid-section');
        const multiplePlansSection = document.getElementById('multiple-plans-section');

        if (type === 'paid') {
            pricingStrategySection.style.display = 'block';
            pricingStrategySection.classList.remove('d-hidden');

            // Show appropriate pricing section based on current strategy
            const currentStrategy = document.querySelector('input[name="pricing_type"]:checked')?.value || 'single';
            if (currentStrategy === 'single') {
                paidSection.style.display = 'block';
                paidSection.classList.remove('d-hidden');
                multiplePlansSection.style.display = 'none';
                multiplePlansSection.classList.add('d-hidden');
            } else {
                multiplePlansSection.style.display = 'block';
                multiplePlansSection.classList.remove('d-hidden');
                paidSection.style.display = 'none';
                paidSection.classList.add('d-hidden');
            }
        } else {
            pricingStrategySection.style.display = 'none';
            pricingStrategySection.classList.add('d-hidden');
            paidSection.style.display = 'none';
            paidSection.classList.add('d-hidden');
            multiplePlansSection.style.display = 'none';
            multiplePlansSection.classList.add('d-hidden');
        }
    }

    function selectPricingStrategy(strategy) {
        // Xóa tất cả validation errors trước khi chuyển đổi
        clearValidationErrors();

        // Remove selected class from all option cards in pricing strategy section
        const strategyCards = document.querySelectorAll('#pricing-strategy-section .option-card');
        strategyCards.forEach(card => {
            card.classList.remove('selected');
        });

        // Add selected class to clicked card
        const selectedCard = event.currentTarget;
        selectedCard.classList.add('selected');

        // Update radio button
        const radio = selectedCard.querySelector('input[type="radio"]');
        radio.checked = true;

        // Show/hide appropriate pricing section
        const paidSection = document.getElementById('paid-section');
        const multiplePlansSection = document.getElementById('multiple-plans-section');

        if (strategy === 'single') {
            paidSection.style.display = 'block';
            paidSection.classList.remove('d-hidden');
            multiplePlansSection.style.display = 'none';
            multiplePlansSection.classList.add('d-hidden');

            // Reset multiple pricing fields khi chuyển về single
            resetMultiplePricingFields();
        } else {
            multiplePlansSection.style.display = 'block';
            multiplePlansSection.classList.remove('d-hidden');
            paidSection.style.display = 'none';
            paidSection.classList.add('d-hidden');

            // Reset single pricing fields khi chuyển sang multiple
            resetSinglePricingFields();
        }
    }

    function clearValidationErrors() {
        // Xóa tất cả error messages
        document.querySelectorAll('.text-danger, .invalid-feedback, .error-message').forEach(el => {
            el.remove();
        });

        // Xóa class error từ inputs
        document.querySelectorAll('.is-invalid, .border-danger').forEach(el => {
            el.classList.remove('is-invalid', 'border-danger');
        });

        // Xóa style border đỏ
        document.querySelectorAll('input, select, textarea').forEach(el => {
            el.style.borderColor = '';
        });
    }

    function resetSinglePricingFields() {
        // Reset single pricing inputs (không xóa giá trị, chỉ xóa validation)
        const priceInput = document.getElementById('price');
        const discountedPriceInput = document.getElementById('discounted_price');

        if (priceInput) {
            priceInput.classList.remove('is-invalid', 'border-danger');
            priceInput.style.borderColor = '';
        }
        if (discountedPriceInput) {
            discountedPriceInput.classList.remove('is-invalid', 'border-danger');
            discountedPriceInput.style.borderColor = '';
        }
    }

    function resetMultiplePricingFields() {
        // Reset multiple pricing validation (không xóa giá trị, chỉ xóa validation)
        const multiplePricingInputs = document.querySelectorAll('#multiple-plans-section input');
        multiplePricingInputs.forEach(input => {
            input.classList.remove('is-invalid', 'border-danger');
            input.style.borderColor = '';
        });
    }

    function toggleDiscount() {
        const toggle = document.getElementById('discount-toggle');
        const checkbox = document.getElementById('discount_flag');
        const priceTrendSection = document.getElementById('price-trend-section');

        toggle.classList.toggle('active');
        checkbox.checked = toggle.classList.contains('active');

        if (checkbox.checked) {
            priceTrendSection.style.display = 'block';
        } else {
            priceTrendSection.style.display = 'none';
        }
    }

    function selectExpiryType(type) {
        // Remove selected class from all option cards in expiry section
        const expiryCards = document.querySelectorAll('.workflow-step .option-card');
        expiryCards.forEach(card => {
            if (card.querySelector('#lifetime_expiry_period') || card.querySelector('#limited_expiry_period')) {
                card.classList.remove('selected');
            }
        });

        // Add selected class to clicked card
        const selectedCard = event.currentTarget;
        selectedCard.classList.add('selected');

        // Update radio button
        const radio = selectedCard.querySelector('input[type="radio"]');
        if (radio) {
            radio.checked = true;
        }

        // Show/hide month input section
        const monthSection = document.getElementById('number_of_month');
        if (monthSection) {
            if (type === 'limited') {
                monthSection.style.display = 'block';
                monthSection.classList.remove('d-hidden');

                // Focus on the input field
                const monthInput = monthSection.querySelector('input[name="number_of_month"]');
                if (monthInput) {
                    setTimeout(() => monthInput.focus(), 300);
                }
            } else {
                monthSection.style.display = 'none';
                monthSection.classList.add('d-hidden');
            }
        }

        // Trigger change event for any additional logic
        if (radio) {
            radio.dispatchEvent(new Event('change'));
        }
    }

    // Enhanced form validation and UX
    function validatePricing() {
        const isPaid = document.querySelector('input[name="is_paid"]:checked').value;
        const price = document.getElementById('price').value;

        if (isPaid === '1' && (!price || price <= 0)) {
            alert('Vui lòng nhập giá khóa học hợp lệ');
            return false;
        }

        return true;
    }

    // Fix number input validation
    function setupNumberInputs() {
        const numberInputs = document.querySelectorAll('input[type="number"]');

        numberInputs.forEach(input => {
            // Remove invalid attributes that cause validation issues
            input.removeAttribute('step');

            // Add custom validation
            input.addEventListener('input', function() {
                let value = this.value;

                // Remove non-numeric characters except decimal point
                value = value.replace(/[^0-9.]/g, '');

                // Ensure only one decimal point
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }

                // Update the input value
                this.value = value;

                // Remove browser validation message
                this.setCustomValidity('');
            });

            // Handle blur event for final validation
            input.addEventListener('blur', function() {
                let value = parseFloat(this.value);

                if (isNaN(value) || value < 0) {
                    this.value = '';
                } else {
                    // Format to remove unnecessary decimals
                    this.value = value.toString();
                }
            });
        });
    }

    // Auto-format currency input
    function formatCurrency(input) {
        let value = input.value.replace(/[^\d]/g, '');
        if (value) {
            value = parseInt(value).toLocaleString('vi-VN');
            input.value = value;
        }
    }

    // Price calculation helper
    function calculateDiscount() {
        const originalPrice = parseFloat(document.getElementById('price').value) || 0;
        const discountedPrice = parseFloat(document.getElementById('discounted_price').value) || 0;

        if (originalPrice > 0 && discountedPrice > 0 && discountedPrice < originalPrice) {
            const discountPercent = ((originalPrice - discountedPrice) / originalPrice * 100).toFixed(1);

            // Show discount percentage
            const helper = document.querySelector('#discounted_price').nextElementSibling;
            if (helper && helper.classList.contains('price-helper')) {
                helper.innerHTML = `<i class="fi-rr-info"></i> Giảm giá ${discountPercent}% so với giá gốc`;
            }
        }
    }

    // Initialize expiry section visibility
    function initializeExpirySection() {
        const limitedRadio = document.getElementById('limited_expiry_period');
        const monthSection = document.getElementById('number_of_month');

        if (limitedRadio && limitedRadio.checked && monthSection) {
            monthSection.style.display = 'block';
            monthSection.classList.remove('d-hidden');
        }
    }

    // Đảm bảo script chạy sau khi DOM đã load
    document.addEventListener('DOMContentLoaded', function() {
        // Setup number inputs to fix validation issues
        setupNumberInputs();

        // Initialize expiry section
        initializeExpirySection();

        // Add event listeners for price inputs
        const priceInput = document.getElementById('price');
        const discountedPriceInput = document.getElementById('discounted_price');

        if (priceInput) {
            priceInput.addEventListener('blur', calculateDiscount);
        }

        if (discountedPriceInput) {
            discountedPriceInput.addEventListener('blur', calculateDiscount);
        }

        // Initialize discount calculation
        calculateDiscount();

        // Add smooth transitions for month section
        const monthSection = document.getElementById('number_of_month');
        if (monthSection) {
            monthSection.style.transition = 'all 0.3s ease';
        }
        // Lấy các input date
        const date1 = document.getElementById('start-date-1');
        const date2 = document.getElementById('start-date-2');
        const date3 = document.getElementById('start-date-3');


        // Kiểm tra nếu đã có ngày đầu tiên được đặt
        if(date1.value) {
            enableSecondDate();
        }

        // Kiểm tra nếu đã có ngày thứ hai được đặt
        if(date2.value) {
            enableThirdDate();
        }

        // Khi ngày đầu tiên thay đổi
        date1.addEventListener('change', function() {
            enableSecondDate();
        });

        // Khi ngày thứ hai thay đổi
        date2.addEventListener('change', function() {
            enableThirdDate();
        });

        // Hàm để kích hoạt ô ngày thứ hai sau khi chọn ngày đầu tiên
        function enableSecondDate() {
            if(date1.value) {
                // Lấy ngày từ ô thứ nhất
                const firstDate = new Date(date1.value);
                // Đặt ngày tối thiểu cho ô thứ hai (ngày sau ngày đầu tiên)
                const nextDay = new Date(firstDate);
                nextDay.setDate(nextDay.getDate() + 1);
                const nextDayFormatted = nextDay.toISOString().split('T')[0];

                date2.min = nextDayFormatted;
                date2.disabled = false;

                // Nếu giá trị hiện tại của date2 nhỏ hơn min, đặt lại nó
                if(date2.value && new Date(date2.value) < nextDay) {
                    date2.value = '';
                }
            } else {
                // Nếu date1 bị xóa, khóa date2 và date3
                date2.disabled = true;
                date2.value = '';
                date3.disabled = true;
                date3.value = '';
            }
        }

        // Hàm để kích hoạt ô ngày thứ ba sau khi chọn ngày thứ hai
        function enableThirdDate() {
            if(date2.value) {
                // Lấy ngày từ ô thứ hai
                const secondDate = new Date(date2.value);
                // Đặt ngày tối thiểu cho ô thứ ba (ngày sau ngày thứ hai)
                const nextDay = new Date(secondDate);
                nextDay.setDate(nextDay.getDate() + 1);
                const nextDayFormatted = nextDay.toISOString().split('T')[0];

                date3.min = nextDayFormatted;
                date3.disabled = false;

                // Nếu giá trị hiện tại của date3 nhỏ hơn min, đặt lại nó
                if(date3.value && new Date(date3.value) < nextDay) {
                    date3.value = '';
                }
            } else {
                // Nếu date2 bị xóa, khóa date3
                date3.disabled = true;
                date3.value = '';
            }
        }

        // Thêm xử lý cho discount_flag checkbox
        document.getElementById('discount_flag').addEventListener('change', togglePriceTrend);
    });

    // Hàm để hiển thị/ẩn phần Price Trend
    function togglePriceTrend() {
        const discountFlag = document.getElementById('discount_flag');
        const priceTrendSection = document.getElementById('price-trend-section');

        if (discountFlag.checked) {
            priceTrendSection.style.display = 'flex'; // hoặc 'block' tùy vào cấu trúc CSS của bạn
        } else {
            priceTrendSection.style.display = 'none';
        }
    }

    // Multiple Pricing Plans JavaScript Functions
    let planIdCounter = 4; // Start from 4 since we have 3 default plans

    function updateDurationField(planId, durationType) {
        const durationField = document.getElementById(`duration-field-${planId}`);
        if (durationType === 'lifetime') {
            durationField.style.display = 'none';
            durationField.querySelector('input').value = '';
        } else {
            durationField.style.display = 'block';
        }
    }

    function togglePopular(planId) {
        // Remove popular from all other plans
        document.querySelectorAll('.plan-item').forEach(item => {
            const itemPlanId = item.getAttribute('data-plan-id');
            const popularBtn = document.getElementById(`popular-btn-${itemPlanId}`);
            const popularInput = document.getElementById(`popular-input-${itemPlanId}`);
            const popularBadge = item.querySelector('.popular-badge');

            if (itemPlanId != planId) {
                popularBtn.textContent = 'Đặt phổ biến';
                popularBtn.style.background = '#6c757d';
                popularInput.value = '0';
                if (popularBadge) {
                    popularBadge.remove();
                }
            }
        });

        // Toggle current plan
        const currentBtn = document.getElementById(`popular-btn-${planId}`);
        const currentInput = document.getElementById(`popular-input-${planId}`);
        const currentItem = document.querySelector(`[data-plan-id="${planId}"]`);

        if (currentInput.value === '1') {
            currentBtn.textContent = 'Đặt phổ biến';
            currentBtn.style.background = '#6c757d';
            currentInput.value = '0';
            const badge = currentItem.querySelector('.popular-badge');
            if (badge) badge.remove();
        } else {
            currentBtn.textContent = 'Phổ biến';
            currentBtn.style.background = 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)';
            currentInput.value = '1';

            // Add popular badge
            if (!currentItem.querySelector('.popular-badge')) {
                const badge = document.createElement('div');
                badge.className = 'popular-badge';
                badge.textContent = 'Phổ biến';
                currentItem.appendChild(badge);
            }
        }
    }

    function removePlan(planId) {
        if (confirm('Bạn có chắc chắn muốn xóa gói giá này?')) {
            const planItem = document.querySelector(`[data-plan-id="${planId}"]`);
            planItem.remove();
        }
    }

    function addNewPlan() {
        const plansList = document.getElementById('pricing-plans-list');
        const newPlanId = planIdCounter++;

        const planHtml = `
            <div class="plan-item" data-plan-id="${newPlanId}">
                <div class="plan-header">
                    <div class="plan-title">
                        <i class="fi-rr-package"></i>
                        Gói giá #${newPlanId}
                    </div>
                    <div class="plan-actions">
                        <button type="button" class="plan-btn popular" onclick="togglePopular(${newPlanId})"
                                id="popular-btn-${newPlanId}" style="background: #6c757d;">
                            Đặt phổ biến
                        </button>
                        <button type="button" class="plan-btn remove" onclick="removePlan(${newPlanId})">
                            <i class="fi-rr-trash"></i>
                        </button>
                    </div>
                </div>

                <div class="plan-fields">
                    <div class="plan-field">
                        <label>Tên gói</label>
                        <input type="text" name="plans[${newPlanId}][name]"
                               value="Gói mới"
                               placeholder="VD: Gói 1 tháng">
                    </div>

                    <div class="plan-field">
                        <label>Loại thời hạn</label>
                        <select name="plans[${newPlanId}][duration_type]"
                                onchange="updateDurationField(${newPlanId}, this.value)">
                            <option value="months">Theo tháng</option>
                            <option value="lifetime">Trọn đời</option>
                        </select>
                    </div>

                    <div class="plan-field" id="duration-field-${newPlanId}">
                        <label>Số tháng</label>
                        <input type="number" name="plans[${newPlanId}][duration_value]"
                               value="" min="1" max="120" placeholder="VD: 6">
                    </div>

                    <div class="plan-field">
                        <label>Giá gốc (VND)</label>
                        <input type="number" name="plans[${newPlanId}][price]"
                               value="" min="0" step="1000" placeholder="VD: 299000">
                    </div>
                </div>

                <div class="plan-fields" style="margin-top: 12px;">
                    <div class="plan-field">
                        <label>Giá khuyến mãi (VND) - Tùy chọn</label>
                        <input type="number" name="plans[${newPlanId}][discounted_price]"
                               value="" min="0" step="1000" placeholder="VD: 199000">
                    </div>

                    <div class="plan-field">
                        <input type="hidden" name="plans[${newPlanId}][id]" value="${newPlanId}">
                        <input type="hidden" name="plans[${newPlanId}][is_popular]"
                               value="0" id="popular-input-${newPlanId}">
                    </div>
                </div>
            </div>
        `;

        plansList.insertAdjacentHTML('beforeend', planHtml);
    }
</script>
