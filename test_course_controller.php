<?php

// Test CourseController processPricingInfo method
require_once 'vendor/autoload.php';

use App\Models\Course;
use App\Http\Controllers\frontend\CourseController;

// Khởi tạo Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Test CourseController processPricingInfo ===\n\n";

// Tạo test course với price_trends
$course = new Course();
$course->title = "Test Course";
$course->slug = "test-course";
$course->user_id = 1;
$course->category_id = 1;
$course->is_paid = 1;
$course->discount_flag = 1;
$course->price = 500000;

// Test với price_trends là array
$priceTrends = [
    [
        'price' => 300000,
        'start_date' => '2025-01-01'
    ],
    [
        'price' => 400000,
        'start_date' => '2025-02-01'
    ],
    [
        'price' => 500000,
        'start_date' => '2025-03-01'
    ]
];

$course->price_trends = $priceTrends;

try {
    $course->save();
    echo "✓ Course created successfully with ID: " . $course->id . "\n";

    // Test processPricingInfo method
    $controller = new CourseController();
    $page_data = [];

    // Sử dụng reflection để gọi private method
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('processPricingInfo');
    $method->setAccessible(true);

    $method->invokeArgs($controller, [$course, &$page_data]);

    echo "✓ processPricingInfo executed successfully\n";
    echo "Page data keys: " . implode(', ', array_keys($page_data)) . "\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} finally {
    // Cleanup
    if (isset($course->id)) {
        $course->delete();
        echo "✓ Test course deleted\n";
    }
}

echo "\n=== Test completed ===\n";
