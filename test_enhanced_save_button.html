<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Save Button</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Professional Save Button Loading States */
        .save-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            min-width: 140px;
            border: 2px solid #007bff;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }

        .save-btn:hover {
            background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
            border-color: #0056b3;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .save-btn.loading {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            border-color: #ffc107;
            color: #212529;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
            animation: loadingPulse 1.5s ease-in-out infinite;
        }

        .save-btn.loading:hover {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            transform: none;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
        }

        @keyframes loadingPulse {
            0%, 100% {
                box-shadow: 0 4px 12px rgba(255, 193, 7, 0.5);
            }
            50% {
                box-shadow: 0 6px 20px rgba(255, 193, 7, 0.7);
            }
        }

        .save-btn .btn-text {
            transition: all 0.3s ease;
            display: inline-block;
        }

        .save-btn .btn-loading {
            transition: all 0.3s ease;
            display: inline-block;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
        }

        .save-btn.loading .btn-text {
            opacity: 0;
            transform: scale(0.8);
        }

        .save-btn.loading .btn-loading {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        /* Success state animation */
        .save-btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-color: #28a745;
            color: white;
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.5);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            }
        }

        /* Error state */
        .save-btn.error {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-color: #dc3545;
            color: white;
            animation: errorShake 0.5s ease-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Spinner customization */
        .save-btn .spinner-border-sm {
            width: 1.2rem;
            height: 1.2rem;
            border-width: 0.2em;
            border-color: #212529;
            border-right-color: transparent;
            animation: spinner-border 0.8s linear infinite;
        }

        .save-btn .btn-loading {
            font-weight: 600;
            color: #212529;
        }

        .demo-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .state-demo {
            display: inline-block;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="demo-container">
                    <h4 class="text-center mb-4">Enhanced Save Button - Bright & Visible</h4>
                    
                    <div class="text-center mb-4">
                        <button type="button" class="btn save-btn" id="saveChangesBtn">
                            <span class="btn-text">Save Changes</span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Đang lưu...
                            </span>
                        </button>
                    </div>

                    <div class="text-center mb-4">
                        <h6>All Button States:</h6>
                        
                        <div class="state-demo">
                            <button class="btn save-btn">
                                <span class="btn-text">Normal State</span>
                            </button>
                            <p><small>Blue gradient - Default state</small></p>
                        </div>

                        <div class="state-demo">
                            <button class="btn save-btn loading">
                                <span class="btn-text">Loading State</span>
                                <span class="btn-loading">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    Đang lưu...
                                </span>
                            </button>
                            <p><small>Orange gradient + pulse + spinner</small></p>
                        </div>

                        <div class="state-demo">
                            <button class="btn save-btn success">
                                <span class="btn-text">Đã lưu!</span>
                            </button>
                            <p><small>Green gradient + pulse animation</small></p>
                        </div>

                        <div class="state-demo">
                            <button class="btn save-btn error">
                                <span class="btn-text">Có lỗi xảy ra</span>
                            </button>
                            <p><small>Red gradient + shake animation</small></p>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-md-3">
                            <button class="btn btn-primary btn-sm" onclick="testLoadingSuccess()">
                                Test Loading → Success
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger btn-sm" onclick="testLoadingError()">
                                Test Loading → Error
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning btn-sm" onclick="testLoadingOnly()">
                                Test Loading Only
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-secondary btn-sm" onclick="resetButton()">
                                Reset Button
                            </button>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Enhanced Features:</h6>
                        <ul class="list-unstyled">
                            <li>✅ <strong>Bright Blue Default</strong> - Nổi bật hơn với gradient xanh</li>
                            <li>✅ <strong>Orange Loading</strong> - Màu cam nổi bật với pulse animation</li>
                            <li>✅ <strong>Bigger Spinner</strong> - Spinner lớn hơn, dễ nhìn hơn</li>
                            <li>✅ <strong>Dark Text on Loading</strong> - Text đen trên nền cam dễ đọc</li>
                            <li>✅ <strong>Immediate Feedback</strong> - Loading hiển thị ngay lập tức</li>
                            <li>✅ <strong>Smooth Transitions</strong> - Chuyển đổi mượt mà giữa các states</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testLoadingSuccess() {
            showLoadingState();
            setTimeout(showSuccessState, 2000);
        }

        function testLoadingError() {
            showLoadingState();
            setTimeout(showErrorState, 2000);
        }

        function testLoadingOnly() {
            showLoadingState();
        }

        function resetButton() {
            hideLoadingState();
        }

        function showLoadingState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            console.log('Showing loading state');
            saveBtn.addClass('loading');
            saveBtn.prop('disabled', true);
            btnText.addClass('d-none');
            btnLoading.removeClass('d-none');
            
            // Force reflow
            saveBtn[0].offsetHeight;
        }
        
        function hideLoadingState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.removeClass('loading success error');
            saveBtn.prop('disabled', false);
            btnText.removeClass('d-none').text('Save Changes');
            btnLoading.addClass('d-none');
        }
        
        function showSuccessState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.removeClass('loading').addClass('success');
            saveBtn.prop('disabled', false);
            btnText.removeClass('d-none').text('Đã lưu!');
            btnLoading.addClass('d-none');
            
            setTimeout(function() {
                saveBtn.removeClass('success');
                btnText.text('Save Changes');
            }, 2000);
        }
        
        function showErrorState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.removeClass('loading').addClass('error');
            saveBtn.prop('disabled', false);
            btnText.removeClass('d-none').text('Có lỗi xảy ra');
            btnLoading.addClass('d-none');
            
            setTimeout(function() {
                saveBtn.removeClass('error');
                btnText.text('Save Changes');
            }, 3000);
        }
    </script>
</body>
</html>
