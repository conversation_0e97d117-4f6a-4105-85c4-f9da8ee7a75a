<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pricing Plans Fixed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .pricing-plans-section {
            border-top: 1px solid #eee;
            padding-top: 20px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .pricing-plans-container {
            display: flex;
            flex-direction: row;
            gap: 16px;
            overflow-x: auto;
            padding: 4px 0;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            padding-right: 40px;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .pricing-plans-container::-webkit-scrollbar {
            display: none;
        }

        .pricing-plan-option {
            position: relative;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px 12px 12px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 150px;
            max-width: 170px;
            flex-shrink: 0;
            height: 150px;
            box-sizing: border-box;
        }

        .pricing-plan-option:hover {
            border-color: #fa8128;
            box-shadow: 0 4px 12px rgba(250, 129, 40, 0.15);
        }

        .pricing-plan-option.selected {
            border-color: #fa8128;
            background: linear-gradient(135deg, #fff5f0 0%, #fff 100%);
            box-shadow: 0 4px 16px rgba(250, 129, 40, 0.2);
        }

        .popular-badge {
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 4px 10px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            z-index: 3;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .plan-content {
            flex: 1;
            width: 100%;
            margin-bottom: 10px;
            margin-top: 5px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .plan-name {
            font-weight: 600;
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
            line-height: 1.1;
            min-height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .plan-duration {
            font-size: 11px;
            color: #666;
            margin-bottom: 6px;
            line-height: 1;
        }

        .plan-price {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
            margin-bottom: 8px;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 10px;
            line-height: 1;
        }

        .discounted-price {
            color: #fa8128;
            font-weight: 600;
            font-size: 12px;
            line-height: 1;
        }

        .plan-radio {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: auto;
        }

        .plan-radio input[type="radio"] {
            display: none;
        }

        .plan-radio .radio-custom {
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;
            transition: all 0.3s ease;
            position: relative;
        }

        .pricing-plan-option.selected .plan-radio .radio-custom {
            border-color: #fa8128;
            background: #fa8128;
        }

        .plan-radio .radio-custom::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .pricing-plan-option.selected .plan-radio .radio-custom::after {
            opacity: 1;
        }

        .pricing-plans-section::after {
            content: '';
            position: absolute;
            top: 20px;
            right: 0;
            width: 30px;
            height: 150px;
            background: linear-gradient(to left, rgba(255,255,255,0.95) 0%, transparent 100%);
            pointer-events: none;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Pricing Plans - Fixed Layout</h5>
                    </div>
                    <div class="card-body">
                        <div class="pricing-plans-section mb-4">
                            <h6 class="fw-bold mb-3">Chọn gói thời gian:</h6>
                            <div class="pricing-plans-container">
                                
                                <div class="pricing-plan-option selected" 
                                     data-plan-id="2"
                                     data-price="200000"
                                     data-original-price="2990000"
                                     onclick="selectPricingPlan(2)">
                                    
                                    <div class="popular-badge">Phổ biến</div>
                                    
                                    <div class="plan-content">
                                        <div class="plan-name">Gói 6 tháng</div>
                                        <div class="plan-duration">6 tháng</div>
                                        <div class="plan-price">
                                            <span class="original-price">2.990.000 ₫</span>
                                            <span class="discounted-price">200.000 ₫</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="2" checked>
                                        <div class="radio-custom"></div>
                                    </div>
                                </div>

                                <div class="pricing-plan-option" 
                                     data-plan-id="1"
                                     data-price="199000"
                                     data-original-price="2990000"
                                     onclick="selectPricingPlan(1)">
                                    
                                    <div class="plan-content">
                                        <div class="plan-name">Gói 1 tháng</div>
                                        <div class="plan-duration">1 tháng</div>
                                        <div class="plan-price">
                                            <span class="original-price">2.990.000 ₫</span>
                                            <span class="discounted-price">199.000 ₫</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="1">
                                        <div class="radio-custom"></div>
                                    </div>
                                </div>

                                <div class="pricing-plan-option" 
                                     data-plan-id="3"
                                     data-price="15000000"
                                     data-original-price="20000000"
                                     onclick="selectPricingPlan(3)">
                                    
                                    <div class="plan-content">
                                        <div class="plan-name">Gói mới</div>
                                        <div class="plan-duration">12 tháng</div>
                                        <div class="plan-price">
                                            <span class="original-price">20.000.000 ₫</span>
                                            <span class="discounted-price">15.000.000 ₫</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="3">
                                        <div class="radio-custom"></div>
                                    </div>
                                </div>

                                <div class="pricing-plan-option" 
                                     data-plan-id="4"
                                     data-price="3000000"
                                     data-original-price="30000000"
                                     onclick="selectPricingPlan(4)">
                                    
                                    <div class="plan-content">
                                        <div class="plan-name">Trọn đời</div>
                                        <div class="plan-duration">Trọn đời</div>
                                        <div class="plan-price">
                                            <span class="original-price">30.000.000 ₫</span>
                                            <span class="discounted-price">3.000.000 ₫</span>
                                        </div>
                                    </div>
                                    
                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="4">
                                        <div class="radio-custom"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="price-info mb-2">
                            <span>Gói đã chọn:</span>
                            <span id="selected-plan-name">Gói 6 tháng</span>
                        </div>
                        <div class="price-info mb-2">
                            <span>Giá gốc:</span>
                            <span class="text-decoration-line-through" id="original-price-display">2.990.000 ₫</span>
                        </div>
                        <div class="price-info mb-4">
                            <span>Giá ưu đãi hôm nay:</span>
                            <strong id="current-price-display">200.000 ₫</strong>
                        </div>

                        <div class="total-price">
                            <span class="fw-bold">Tổng thanh toán:</span>
                            <span class="price text-danger fw-bold" id="total_amount">200.000 ₫</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function selectPricingPlan(planId) {
            const selectedOption = document.querySelector(`[data-plan-id="${planId}"]`);
            if (!selectedOption) return;

            document.querySelectorAll('.pricing-plan-option').forEach(option => {
                option.classList.remove('selected');
            });

            selectedOption.classList.add('selected');

            const radioButton = selectedOption.querySelector('input[type="radio"]');
            if (radioButton) {
                radioButton.checked = true;
            }

            const planPrice = parseFloat(selectedOption.dataset.price);
            const originalPlanPrice = parseFloat(selectedOption.dataset.originalPrice);
            const planName = selectedOption.querySelector('.plan-name').textContent;

            updatePriceDisplay(planName, originalPlanPrice, planPrice);
        }

        function updatePriceDisplay(planName, originalPlanPrice, currentPrice) {
            document.getElementById('selected-plan-name').textContent = planName;
            document.getElementById('original-price-display').textContent = formatCurrency(originalPlanPrice);
            document.getElementById('current-price-display').textContent = formatCurrency(currentPrice);
            document.getElementById('total_amount').textContent = formatCurrency(currentPrice);
        }
    </script>
</body>
</html>
