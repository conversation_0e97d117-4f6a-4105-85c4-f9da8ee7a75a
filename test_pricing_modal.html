<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pricing Plans Modal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Pricing Plans Styles */
        .pricing-plans-section {
            border-top: 1px solid #eee;
            padding-top: 20px;
            margin-top: 20px;
        }

        .pricing-plans-container {
            display: flex;
            flex-direction: row;
            gap: 12px;
            overflow-x: auto;
            padding: 4px 0;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        .pricing-plans-container::-webkit-scrollbar {
            height: 6px;
        }

        .pricing-plans-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .pricing-plans-container::-webkit-scrollbar-thumb {
            background: #fa8128;
            border-radius: 3px;
        }

        .pricing-plan-option {
            position: relative;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            min-width: 140px;
            max-width: 160px;
            flex-shrink: 0;
        }

        .pricing-plan-option:hover {
            border-color: #fa8128;
            box-shadow: 0 4px 12px rgba(250, 129, 40, 0.15);
        }

        .pricing-plan-option.selected {
            border-color: #fa8128;
            background: linear-gradient(135deg, #fff5f0 0%, #fff 100%);
            box-shadow: 0 4px 16px rgba(250, 129, 40, 0.2);
        }

        .popular-badge {
            position: absolute;
            top: -6px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            z-index: 2;
            white-space: nowrap;
        }

        .plan-content {
            flex: 1;
            width: 100%;
            margin-bottom: 12px;
        }

        .plan-name {
            font-weight: 600;
            font-size: 14px;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.2;
        }

        .plan-duration {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .plan-price {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 11px;
            line-height: 1;
        }

        .discounted-price {
            color: #fa8128;
            font-weight: 600;
            font-size: 13px;
            line-height: 1;
        }

        .current-price {
            color: #fa8128;
            font-weight: 600;
            font-size: 13px;
            line-height: 1;
        }

        .plan-radio {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: auto;
        }

        .plan-radio input[type="radio"] {
            width: 18px;
            height: 18px;
            accent-color: #fa8128;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Pricing Plans Modal</h5>
                    </div>
                    <div class="card-body">
                        <!-- Pricing Plans Selection -->
                        <div class="pricing-plans-section mb-4">
                            <h6 class="fw-bold mb-3">Chọn gói thời gian:</h6>
                            <div class="pricing-plans-container">

                                <!-- Gói phổ biến lên đầu -->
                                <div class="pricing-plan-option selected"
                                     data-plan-id="2"
                                     data-price="999000"
                                     data-original-price="1500000"
                                     onclick="selectPricingPlan(2)">

                                    <div class="popular-badge">Phổ biến</div>

                                    <div class="plan-content">
                                        <div class="plan-name">Gói 6 tháng</div>
                                        <div class="plan-duration">6 tháng</div>
                                        <div class="plan-price">
                                            <span class="original-price">1,500,000 ₫</span>
                                            <span class="discounted-price">999,000 ₫</span>
                                        </div>
                                    </div>

                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="2" checked>
                                    </div>
                                </div>

                                <div class="pricing-plan-option"
                                     data-plan-id="1"
                                     data-price="199000"
                                     data-original-price="299000"
                                     onclick="selectPricingPlan(1)">

                                    <div class="plan-content">
                                        <div class="plan-name">Gói 1 tháng</div>
                                        <div class="plan-duration">1 tháng</div>
                                        <div class="plan-price">
                                            <span class="original-price">299,000 ₫</span>
                                            <span class="discounted-price">199,000 ₫</span>
                                        </div>
                                    </div>

                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="1">
                                    </div>
                                </div>

                                <div class="pricing-plan-option"
                                     data-plan-id="3"
                                     data-price="1999000"
                                     data-original-price="2999000"
                                     onclick="selectPricingPlan(3)">

                                    <div class="plan-content">
                                        <div class="plan-name">Trọn đời</div>
                                        <div class="plan-duration">Trọn đời</div>
                                        <div class="plan-price">
                                            <span class="original-price">2,999,000 ₫</span>
                                            <span class="discounted-price">1,999,000 ₫</span>
                                        </div>
                                    </div>

                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="3">
                                    </div>
                                </div>

                                <!-- Thêm gói thứ 4 để test scroll -->
                                <div class="pricing-plan-option"
                                     data-plan-id="4"
                                     data-price="2999000"
                                     data-original-price="3999000"
                                     onclick="selectPricingPlan(4)">

                                    <div class="plan-content">
                                        <div class="plan-name">Gói VIP</div>
                                        <div class="plan-duration">2 năm</div>
                                        <div class="plan-price">
                                            <span class="original-price">3,999,000 ₫</span>
                                            <span class="discounted-price">2,999,000 ₫</span>
                                        </div>
                                    </div>

                                    <div class="plan-radio">
                                        <input type="radio" name="pricing_plan_id" value="4">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected Plan Price Display -->
                        <div class="price-info mb-2">
                            <span>Gói đã chọn:</span>
                            <span id="selected-plan-name">Gói 6 tháng</span>
                        </div>
                        <div class="price-info mb-2">
                            <span>Giá gốc:</span>
                            <span class="text-decoration-line-through" id="original-price-display">1,500,000 ₫</span>
                        </div>
                        <div class="price-info mb-4">
                            <span>Giá ưu đãi hôm nay:</span>
                            <strong id="current-price-display">999,000 ₫</strong>
                        </div>

                        <div class="total-price">
                            <span class="fw-bold">Tổng thanh toán:</span>
                            <span class="price text-danger fw-bold" id="total_amount">999,000 ₫</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function selectPricingPlan(planId) {
            // Tìm plan option được chọn
            const selectedOption = document.querySelector(`[data-plan-id="${planId}"]`);
            if (!selectedOption) return;

            // Xóa class selected từ tất cả options
            document.querySelectorAll('.pricing-plan-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Thêm class selected cho option được chọn
            selectedOption.classList.add('selected');

            // Cập nhật radio button
            const radioButton = selectedOption.querySelector('input[type="radio"]');
            if (radioButton) {
                radioButton.checked = true;
            }

            // Lấy thông tin plan
            const planPrice = parseFloat(selectedOption.dataset.price);
            const originalPlanPrice = parseFloat(selectedOption.dataset.originalPrice);
            const planName = selectedOption.querySelector('.plan-name').textContent;

            // Cập nhật hiển thị giá
            updatePriceDisplay(planName, originalPlanPrice, planPrice);
        }

        function updatePriceDisplay(planName, originalPlanPrice, currentPrice) {
            // Cập nhật tên plan
            const selectedPlanNameEl = document.getElementById('selected-plan-name');
            if (selectedPlanNameEl) {
                selectedPlanNameEl.textContent = planName;
            }

            // Cập nhật giá gốc
            const originalPriceEl = document.getElementById('original-price-display');
            if (originalPriceEl) {
                originalPriceEl.textContent = formatCurrency(originalPlanPrice);
            }

            // Cập nhật giá hiện tại
            const currentPriceEl = document.getElementById('current-price-display');
            if (currentPriceEl) {
                currentPriceEl.textContent = formatCurrency(currentPrice);
            }

            // Cập nhật tổng thanh toán
            const totalAmountEl = document.getElementById('total_amount');
            if (totalAmountEl) {
                totalAmountEl.textContent = formatCurrency(currentPrice);
            }
        }
    </script>
</body>
</html>
