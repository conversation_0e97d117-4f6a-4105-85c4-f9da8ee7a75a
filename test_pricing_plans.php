<?php

// Test script để kiểm tra pricing plans functionality
// Chạy từ root directory: php test_pricing_plans.php

require_once 'vendor/autoload.php';

use App\Models\Course;

// Khởi tạo Laravel app
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Test Pricing Plans Functionality ===\n\n";

// Test 1: Tạo course với multiple pricing plans
echo "Test 1: Tạo course với multiple pricing plans\n";

$course = new Course();
$course->title = "Test Course with Multiple Plans";
$course->slug = "test-course-multiple-plans";
$course->user_id = 1;
$course->category_id = 1;
$course->is_paid = 1;
$course->pricing_type = 'multiple_plans';
$course->price = 299000;
$course->discount_flag = 1;

// Tạo pricing plans
$pricingPlans = [
    [
        'id' => 1,
        'name' => 'Gói 1 tháng',
        'duration_type' => 'months',
        'duration_value' => 1,
        'price' => 299000,
        'discounted_price' => 199000,
        'is_popular' => false
    ],
    [
        'id' => 2,
        'name' => 'Gói 6 tháng',
        'duration_type' => 'months',
        'duration_value' => 6,
        'price' => 1500000,
        'discounted_price' => 999000,
        'is_popular' => true
    ],
    [
        'id' => 3,
        'name' => 'Trọn đời',
        'duration_type' => 'lifetime',
        'duration_value' => null,
        'price' => 2999000,
        'discounted_price' => 1999000,
        'is_popular' => false
    ]
];

$course->pricing_plans = $pricingPlans;

try {
    $course->save();
    echo "✓ Course created successfully with ID: " . $course->id . "\n";
} catch (Exception $e) {
    echo "✗ Error creating course: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Kiểm tra hasMultiplePricingPlans
echo "\nTest 2: Kiểm tra hasMultiplePricingPlans\n";
if ($course->hasMultiplePricingPlans()) {
    echo "✓ hasMultiplePricingPlans() returned true\n";
} else {
    echo "✗ hasMultiplePricingPlans() returned false\n";
}

// Test 3: Lấy danh sách pricing plans
echo "\nTest 3: Lấy danh sách pricing plans\n";
$plans = $course->getPricingPlans();
echo "Number of plans: " . count($plans) . "\n";
foreach ($plans as $plan) {
    echo "- Plan {$plan['id']}: {$plan['name']} - " . number_format($plan['price']) . " VND";
    if ($plan['is_popular']) {
        echo " (Popular)";
    }
    echo "\n";
}

// Test 4: Lấy plan theo ID
echo "\nTest 4: Lấy plan theo ID\n";
$plan = $course->getPricingPlanById(2);
if ($plan) {
    echo "✓ Found plan: {$plan['name']}\n";
} else {
    echo "✗ Plan not found\n";
}

// Test 5: Tính giá hiện tại của plan
echo "\nTest 5: Tính giá hiện tại của plan\n";
$currentPrice = $course->getPlanCurrentPrice(2);
echo "Current price for plan 2: " . number_format($currentPrice) . " VND\n";

// Test 6: Lấy popular plan
echo "\nTest 6: Lấy popular plan\n";
$popularPlan = $course->getPopularPlan();
if ($popularPlan) {
    echo "✓ Popular plan: {$popularPlan['name']}\n";
} else {
    echo "✗ No popular plan found\n";
}

// Test 7: Tính toán ngày hết hạn
echo "\nTest 7: Tính toán ngày hết hạn\n";
$expiryDate = $course->calculateExpiryDate(1); // 1 tháng
if ($expiryDate) {
    echo "Expiry date for 1-month plan: " . date('Y-m-d H:i:s', $expiryDate) . "\n";
} else {
    echo "No expiry date\n";
}

$expiryDate = $course->calculateExpiryDate(3); // Lifetime
if ($expiryDate) {
    echo "Expiry date for lifetime plan: " . date('Y-m-d H:i:s', $expiryDate) . "\n";
} else {
    echo "✓ Lifetime plan has no expiry date\n";
}

// Test 8: Test helper function
echo "\nTest 8: Test helper function get_course_pricing_info\n";
$pricingInfo = get_course_pricing_info($course->id, 2);
if ($pricingInfo) {
    echo "✓ Pricing info retrieved successfully\n";
    echo "- Pricing type: {$pricingInfo['pricing_type']}\n";
    echo "- Has multiple plans: " . ($pricingInfo['has_multiple_plans'] ? 'Yes' : 'No') . "\n";
    echo "- Current price: " . number_format($pricingInfo['current_price']) . " VND\n";
} else {
    echo "✗ Failed to get pricing info\n";
}

// Cleanup
echo "\nCleaning up...\n";
try {
    $course->delete();
    echo "✓ Test course deleted successfully\n";
} catch (Exception $e) {
    echo "✗ Error deleting course: " . $e->getMessage() . "\n";
}

echo "\n=== All tests completed ===\n";
