<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pricing Validation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .option-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .option-card.selected {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .form-validation-error-label {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .d-hidden {
            display: none !important;
        }
        
        .pricing-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Pricing Validation Logic</h5>
                    </div>
                    <div class="card-body">
                        
                        <!-- Pricing Strategy Selection -->
                        <div class="mb-4">
                            <h6>Chọn chiến lược giá:</h6>
                            
                            <div class="option-card selected" onclick="selectPricingStrategy('single')">
                                <input type="radio" name="pricing_type" value="single" checked style="display: none;">
                                <div class="option-title">Giá đơn giản</div>
                                <div class="option-description">Một mức giá duy nhất cho khóa học</div>
                            </div>

                            <div class="option-card" onclick="selectPricingStrategy('multiple_plans')">
                                <input type="radio" name="pricing_type" value="multiple_plans" style="display: none;">
                                <div class="option-title">Nhiều gói giá</div>
                                <div class="option-description">Tạo nhiều gói giá khác nhau (1 tháng, 6 tháng, trọn đời...)</div>
                            </div>
                        </div>

                        <!-- Single Pricing Section -->
                        <div class="pricing-section" id="single-pricing">
                            <h6>Cấu hình giá đơn giản:</h6>
                            
                            <div class="mb-3">
                                <label>Giá gốc (VND)</label>
                                <input type="number" name="price" id="price" class="form-control" 
                                       placeholder="VD: 299000" min="0">
                            </div>
                            
                            <div class="mb-3">
                                <label>
                                    <input type="checkbox" name="discount_flag" id="discount_flag" value="1">
                                    Có giá khuyến mãi
                                </label>
                            </div>
                            
                            <div class="mb-3" id="discount-section" style="display: none;">
                                <label>Giá khuyến mãi (VND)</label>
                                <input type="number" name="discounted_price" id="discounted_price" class="form-control" 
                                       placeholder="VD: 199000" min="0">
                            </div>
                        </div>

                        <!-- Multiple Pricing Section -->
                        <div class="pricing-section d-hidden" id="multiple-pricing">
                            <h6>Cấu hình nhiều gói giá:</h6>
                            
                            <div class="mb-3">
                                <label>Tên gói #1</label>
                                <input type="text" name="plans[1][name]" class="form-control" 
                                       placeholder="VD: Gói 1 tháng">
                            </div>
                            
                            <div class="mb-3">
                                <label>Giá gói #1 (VND)</label>
                                <input type="number" name="plans[1][price]" class="form-control" 
                                       placeholder="VD: 199000" min="0">
                            </div>
                        </div>

                        <!-- Test Buttons -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-primary" onclick="testValidation()">
                                Test Validation
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="clearValidationErrors()">
                                Clear Errors
                            </button>
                            <button type="button" class="btn btn-warning" onclick="simulateServerErrors()">
                                Simulate Server Errors
                            </button>
                        </div>

                        <!-- Results -->
                        <div class="mt-4" id="results">
                            <h6>Kết quả test:</h6>
                            <div id="result-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectPricingStrategy(strategy) {
            // Clear validation errors
            clearValidationErrors();
            
            // Update UI
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            event.currentTarget.classList.add('selected');
            
            const radio = event.currentTarget.querySelector('input[type="radio"]');
            radio.checked = true;
            
            // Show/hide sections
            const singlePricing = document.getElementById('single-pricing');
            const multiplePricing = document.getElementById('multiple-pricing');
            
            if (strategy === 'single') {
                singlePricing.classList.remove('d-hidden');
                multiplePricing.classList.add('d-hidden');
                resetMultiplePricingFields();
            } else {
                singlePricing.classList.add('d-hidden');
                multiplePricing.classList.remove('d-hidden');
                resetSinglePricingFields();
            }
            
            updateResults('Chuyển đổi sang: ' + strategy);
        }

        function clearValidationErrors() {
            // Remove error messages
            document.querySelectorAll('.form-validation-error-label').forEach(el => {
                el.remove();
            });
            
            // Remove error styling
            document.querySelectorAll('.is-invalid, .border-danger').forEach(el => {
                el.classList.remove('is-invalid', 'border-danger');
                el.style.borderColor = '';
            });
            
            updateResults('Đã xóa tất cả validation errors');
        }

        function resetSinglePricingFields() {
            const priceInput = document.getElementById('price');
            const discountedPriceInput = document.getElementById('discounted_price');
            const discountFlag = document.getElementById('discount_flag');
            
            if (priceInput) {
                priceInput.classList.remove('is-invalid', 'border-danger');
                priceInput.style.borderColor = '';
            }
            if (discountedPriceInput) {
                discountedPriceInput.classList.remove('is-invalid', 'border-danger');
                discountedPriceInput.style.borderColor = '';
            }
        }

        function resetMultiplePricingFields() {
            const multiplePricingInputs = document.querySelectorAll('#multiple-pricing input');
            multiplePricingInputs.forEach(input => {
                input.classList.remove('is-invalid', 'border-danger');
                input.style.borderColor = '';
            });
        }

        function testValidation() {
            const strategy = document.querySelector('input[name="pricing_type"]:checked').value;
            let errors = [];
            
            if (strategy === 'single') {
                const price = document.getElementById('price').value;
                const discountFlag = document.getElementById('discount_flag').checked;
                const discountedPrice = document.getElementById('discounted_price').value;
                
                if (!price || price <= 0) {
                    errors.push('price: Giá gốc là bắt buộc khi khóa học có phí');
                }
                
                if (discountFlag && (!discountedPrice || discountedPrice <= 0)) {
                    errors.push('discounted_price: Giá khuyến mãi là bắt buộc khi bật khuyến mãi');
                }
            } else {
                const planName = document.querySelector('input[name="plans[1][name]"]').value;
                const planPrice = document.querySelector('input[name="plans[1][price]"]').value;
                
                if (!planName) {
                    errors.push('plans.1.name: Tên gói là bắt buộc');
                }
                
                if (!planPrice || planPrice <= 0) {
                    errors.push('plans.1.price: Giá gói là bắt buộc');
                }
            }
            
            if (errors.length > 0) {
                updateResults('Validation Errors Found:<br>' + errors.join('<br>'));
                simulateServerErrors();
            } else {
                updateResults('✅ Validation passed! No errors found.');
            }
        }

        function simulateServerErrors() {
            // Simulate server validation response
            const mockResponse = {
                validationError: {
                    'price': ['Giá gốc là bắt buộc khi khóa học có phí'],
                    'discounted_price': ['Giá khuyến mãi là bắt buộc khi bật khuyến mãi']
                }
            };
            
            // Clear existing errors
            clearValidationErrors();
            
            // Add error messages like the real system
            Object.keys(mockResponse.validationError).forEach(key => {
                const input = document.querySelector(`[name="${key}"]`);
                if (input) {
                    const errorMsg = document.createElement('small');
                    errorMsg.className = 'text-danger text-12px form-validation-error-label';
                    errorMsg.textContent = mockResponse.validationError[key][0];
                    input.parentNode.appendChild(errorMsg);
                    
                    input.classList.add('is-invalid');
                    input.style.borderColor = '#dc3545';
                }
            });
            
            updateResults('Simulated server validation errors added');
        }

        function updateResults(message) {
            const resultContent = document.getElementById('result-content');
            const timestamp = new Date().toLocaleTimeString();
            resultContent.innerHTML = `<div class="alert alert-info">[${timestamp}] ${message}</div>` + resultContent.innerHTML;
        }

        // Setup discount toggle
        document.getElementById('discount_flag').addEventListener('change', function() {
            const discountSection = document.getElementById('discount-section');
            if (this.checked) {
                discountSection.style.display = 'block';
            } else {
                discountSection.style.display = 'none';
                document.getElementById('discounted_price').value = '';
            }
        });

        // Setup input event listeners
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.remove('is-invalid', 'border-danger');
                this.style.borderColor = '';
                const errorLabel = this.parentNode.querySelector('.form-validation-error-label');
                if (errorLabel) {
                    errorLabel.remove();
                }
            });
        });

        updateResults('Test page loaded successfully');
    </script>
</body>
</html>
