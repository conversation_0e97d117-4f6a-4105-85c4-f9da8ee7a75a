<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Save Button Loading</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Professional Save Button Loading States */
        .save-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            min-width: 140px;
            border: 2px solid #6c757d;
            background: transparent;
            color: #6c757d;
        }

        .save-btn:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .save-btn.loading {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-color: #28a745;
            color: white;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        .save-btn.loading:hover {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transform: none;
        }

        .save-btn .btn-text {
            transition: opacity 0.3s ease;
        }

        .save-btn .btn-loading {
            transition: opacity 0.3s ease;
        }

        .save-btn.loading .btn-text {
            opacity: 0;
        }

        .save-btn.loading .btn-loading {
            opacity: 1;
        }

        /* Success state animation */
        .save-btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-color: #28a745;
            color: white;
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.5);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            }
        }

        /* Error state */
        .save-btn.error {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-color: #dc3545;
            color: white;
            animation: errorShake 0.5s ease-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Spinner customization */
        .save-btn .spinner-border-sm {
            width: 1rem;
            height: 1rem;
            border-width: 0.15em;
        }

        /* Ripple effect */
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }

        .demo-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="demo-container">
                    <h4 class="text-center mb-4">Professional Save Button Loading Demo</h4>
                    
                    <div class="text-center mb-4">
                        <button type="button" class="btn save-btn" id="saveChangesBtn">
                            <span class="btn-text">Save Changes</span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Đang lưu...
                            </span>
                        </button>
                    </div>

                    <div class="row">
                        <div class="col-md-4 text-center">
                            <button class="btn btn-primary btn-sm" onclick="testLoadingSuccess()">
                                Test Loading → Success
                            </button>
                        </div>
                        <div class="col-md-4 text-center">
                            <button class="btn btn-danger btn-sm" onclick="testLoadingError()">
                                Test Loading → Error
                            </button>
                        </div>
                        <div class="col-md-4 text-center">
                            <button class="btn btn-secondary btn-sm" onclick="resetButton()">
                                Reset Button
                            </button>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>Features:</h6>
                        <ul class="list-unstyled">
                            <li>✅ <strong>Smooth transitions</strong> - Mượt mà khi chuyển đổi states</li>
                            <li>✅ <strong>Loading spinner</strong> - Spinner chuyên nghiệp với text "Đang lưu..."</li>
                            <li>✅ <strong>Success animation</strong> - Pulse effect khi lưu thành công</li>
                            <li>✅ <strong>Error feedback</strong> - Shake animation khi có lỗi</li>
                            <li>✅ <strong>Ripple effect</strong> - Hiệu ứng ripple khi click</li>
                            <li>✅ <strong>Auto reset</strong> - Tự động reset về trạng thái ban đầu</li>
                            <li>✅ <strong>Disabled state</strong> - Không cho click khi đang loading</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testLoadingSuccess() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            // Show loading
            showLoadingState();
            
            // Simulate success after 2 seconds
            setTimeout(function() {
                showSuccessState();
            }, 2000);
        }

        function testLoadingError() {
            const saveBtn = $('#saveChangesBtn');
            
            // Show loading
            showLoadingState();
            
            // Simulate error after 2 seconds
            setTimeout(function() {
                showErrorState();
            }, 2000);
        }

        function resetButton() {
            hideLoadingState();
        }

        function showLoadingState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.addClass('loading');
            saveBtn.prop('disabled', true);
            btnText.addClass('d-none');
            btnLoading.removeClass('d-none');
        }
        
        function hideLoadingState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.removeClass('loading success error');
            saveBtn.prop('disabled', false);
            btnText.removeClass('d-none').text('Save Changes');
            btnLoading.addClass('d-none');
        }
        
        function showSuccessState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.removeClass('loading').addClass('success');
            saveBtn.prop('disabled', false);
            btnText.removeClass('d-none').text('Đã lưu!');
            btnLoading.addClass('d-none');
            
            // Reset after 2 seconds
            setTimeout(function() {
                saveBtn.removeClass('success');
                btnText.text('Save Changes');
            }, 2000);
        }
        
        function showErrorState() {
            const saveBtn = $('#saveChangesBtn');
            const btnText = saveBtn.find('.btn-text');
            const btnLoading = saveBtn.find('.btn-loading');
            
            saveBtn.removeClass('loading').addClass('error');
            saveBtn.prop('disabled', false);
            btnText.removeClass('d-none').text('Có lỗi xảy ra');
            btnLoading.addClass('d-none');
            
            // Reset after 3 seconds
            setTimeout(function() {
                saveBtn.removeClass('error');
                btnText.text('Save Changes');
            }, 3000);
        }

        // Ripple effect
        function createRippleEffect(button, event) {
            const ripple = document.createElement('span');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;
            
            button.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        $(document).ready(function() {
            // Add ripple effect to save button
            $('#saveChangesBtn').on('click', function(e) {
                createRippleEffect(this, e);
            });
        });
    </script>
</body>
</html>
