<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Validation Display</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .pricing-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .validation-summary {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
            border-color: #f5c6cb;
            animation: slideDown 0.3s ease-out;
        }

        .validation-summary h6 {
            color: #721c24;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .validation-summary ul li {
            color: #721c24;
            margin-bottom: 5px;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .plan-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
        }

        .form-validation-error-label {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }

        .is-invalid {
            border-color: #dc3545 !important;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="pricing-container">
                    <h4>Test Validation Display</h4>
                    
                    <!-- Simulate Multiple Plans -->
                    <div class="plan-item">
                        <h6>Gói giá #1</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <label>Tên gói</label>
                                <input type="text" name="plans[0][name]" class="form-control" placeholder="VD: Gói 1 tháng">
                            </div>
                            <div class="col-md-2">
                                <label>Loại thời hạn</label>
                                <select name="plans[0][duration_type]" class="form-control">
                                    <option value="months">Theo tháng</option>
                                    <option value="lifetime">Trọn đời</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>Số tháng</label>
                                <input type="number" name="plans[0][duration_value]" class="form-control" placeholder="1">
                            </div>
                            <div class="col-md-3">
                                <label>Giá gốc (VND)</label>
                                <input type="number" name="plans[0][price]" class="form-control" placeholder="299000">
                            </div>
                            <div class="col-md-2">
                                <label>Giá KM (VND)</label>
                                <input type="number" name="plans[0][discounted_price]" class="form-control" placeholder="199000">
                            </div>
                        </div>
                    </div>

                    <div class="plan-item">
                        <h6>Gói giá #2</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <label>Tên gói</label>
                                <input type="text" name="plans[1][name]" class="form-control" placeholder="VD: Gói 6 tháng">
                            </div>
                            <div class="col-md-2">
                                <label>Loại thời hạn</label>
                                <select name="plans[1][duration_type]" class="form-control">
                                    <option value="months">Theo tháng</option>
                                    <option value="lifetime">Trọn đời</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>Số tháng</label>
                                <input type="number" name="plans[1][duration_value]" class="form-control" placeholder="6">
                            </div>
                            <div class="col-md-3">
                                <label>Giá gốc (VND)</label>
                                <input type="number" name="plans[1][price]" class="form-control" placeholder="1500000">
                            </div>
                            <div class="col-md-2">
                                <label>Giá KM (VND)</label>
                                <input type="number" name="plans[1][discounted_price]" class="form-control" placeholder="999000">
                            </div>
                        </div>
                    </div>

                    <!-- Test Buttons -->
                    <div class="mt-4">
                        <button type="button" class="btn btn-danger" onclick="simulateValidationErrors()">
                            Simulate Validation Errors
                        </button>
                        <button type="button" class="btn btn-warning" onclick="simulateServerErrors()">
                            Simulate Server Response
                        </button>
                        <button type="button" class="btn btn-success" onclick="clearAllErrors()">
                            Clear All Errors
                        </button>
                        <button type="button" class="btn btn-info" onclick="testScrollToTop()">
                            Test Scroll to Top
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function handleValidationErrors(response) {
            if (typeof response.validationError != "undefined" && response.validationError != 0) {
                // Clear existing errors
                $('.form-validation-error-label').remove();
                
                // Scroll to top để user thấy errors
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
                
                // Show general error message
                showValidationSummary(response.validationError);
                
                // Add specific field errors
                Object.keys(response.validationError).forEach(key => {
                    let fieldName = key;
                    let input = null;
                    
                    // Handle nested field names like plans.0.name
                    if (key.includes('.')) {
                        const parts = key.split('.');
                        if (parts[0] === 'plans') {
                            // Convert plans.0.name to plans[0][name]
                            fieldName = `${parts[0]}[${parts[1]}][${parts[2]}]`;
                        }
                    }
                    
                    input = $(`[name="${fieldName}"]`);
                    
                    if (input.length > 0) {
                        input.after(
                            '<small class="text-danger text-12px form-validation-error-label">' + 
                            response.validationError[key][0] + '</small>'
                        );
                        input.addClass('is-invalid');
                        input.css('border-color', '#dc3545');
                    }
                });
                
                return true;
            }
            return false;
        }
        
        function showValidationSummary(errors) {
            // Remove existing summary
            $('.validation-summary').remove();
            
            // Create error summary
            let errorList = '<div class="alert alert-danger validation-summary mt-3"><h6>Vui lòng sửa các lỗi sau:</h6><ul class="mb-0">';
            Object.keys(errors).forEach(key => {
                errorList += '<li>' + errors[key][0] + '</li>';
            });
            errorList += '</ul></div>';
            
            // Add to top of pricing container
            $('.pricing-container').prepend(errorList);
            
            // Auto remove after 10 seconds
            setTimeout(function() {
                $('.validation-summary').fadeOut();
            }, 10000);
        }

        function simulateValidationErrors() {
            const mockErrors = {
                validationError: {
                    'plans.0.name': ['Tên gói 1 là bắt buộc'],
                    'plans.0.price': ['Giá gói 1 phải lớn hơn 0'],
                    'plans.1.name': ['Tên gói 2 là bắt buộc'],
                    'plans.1.duration_value': ['Số tháng của gói 2 phải lớn hơn 0']
                }
            };
            
            handleValidationErrors(mockErrors);
        }

        function simulateServerErrors() {
            const serverResponse = JSON.stringify({
                validationError: {
                    'plans.0.name': ['Tên gói không được để trống'],
                    'plans.0.price': ['Giá phải là số dương'],
                    'plans.1.duration_value': ['Số tháng không hợp lệ']
                }
            });
            
            handleValidationErrors(JSON.parse(serverResponse));
        }

        function clearAllErrors() {
            $('.form-validation-error-label').remove();
            $('.validation-summary').remove();
            $('.is-invalid').removeClass('is-invalid');
            $('input, select').css('border-color', '');
        }

        function testScrollToTop() {
            // Scroll to bottom first
            $('html, body').animate({
                scrollTop: $(document).height()
            }, 500, function() {
                // Then scroll to top
                setTimeout(function() {
                    $('html, body').animate({
                        scrollTop: 0
                    }, 500);
                }, 1000);
            });
        }

        // Setup input focus handlers
        $(document).ready(function() {
            $('input, select, textarea').on('focus', function() {
                $(this).removeClass('is-invalid');
                $(this).css('border-color', '');
                $(this).siblings('.form-validation-error-label').remove();
                
                // Remove validation summary if all errors are cleared
                setTimeout(function() {
                    if ($('.form-validation-error-label').length === 0) {
                        $('.validation-summary').fadeOut();
                    }
                }, 100);
            });
        });
    </script>
</body>
</html>
